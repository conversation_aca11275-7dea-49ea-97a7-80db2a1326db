package cn.gson.financial.kernel.model.mapper;

import cn.gson.financial.kernel.model.entity.BatchImportTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 批量导入任务Mapper
 */
@Mapper
public interface BatchImportTaskMapper extends BaseMapper<BatchImportTask> {
    
    /**
     * 根据账套ID和用户ID查询任务列表
     */
    @Select("SELECT * FROM fxy_financial_batch_import_task " +
            "WHERE account_sets_id = #{accountSetsId} AND create_user = #{createUser} " +
            "ORDER BY created_time DESC")
    List<BatchImportTask> selectByAccountSetsAndUser(@Param("accountSetsId") Integer accountSetsId, 
                                                     @Param("createUser") Integer createUser);
    
    /**
     * 根据状态查询任务列表
     */
    @Select("SELECT * FROM fxy_financial_batch_import_task " +
            "WHERE account_sets_id = #{accountSetsId} AND status = #{status} " +
            "ORDER BY created_time DESC")
    List<BatchImportTask> selectByAccountSetsAndStatus(@Param("accountSetsId") Integer accountSetsId, 
                                                       @Param("status") String status);
    
    /**
     * 更新任务进度
     */
    @Update("UPDATE fxy_financial_batch_import_task SET " +
            "processed_files = #{processedFiles}, " +
            "processed_images = #{processedImages}, " +
            "success_count = #{successCount}, " +
            "failed_count = #{failedCount}, " +
            "progress_percentage = #{progressPercentage}, " +
            "updated_time = NOW() " +
            "WHERE task_id = #{taskId}")
    int updateProgress(@Param("taskId") String taskId,
                      @Param("processedFiles") Integer processedFiles,
                      @Param("processedImages") Integer processedImages,
                      @Param("successCount") Integer successCount,
                      @Param("failedCount") Integer failedCount,
                      @Param("progressPercentage") BigDecimal progressPercentage);
    
    /**
     * 更新任务状态
     */
    @Update("UPDATE fxy_financial_batch_import_task SET " +
            "status = #{status}, " +
            "error_message = #{errorMessage}, " +
            "updated_time = NOW() " +
            "WHERE task_id = #{taskId}")
    int updateStatus(@Param("taskId") String taskId,
                    @Param("status") String status,
                    @Param("errorMessage") String errorMessage);

    /**
     * 只更新进度百分比
     */
    @Update("UPDATE fxy_financial_batch_import_task SET " +
            "progress_percentage = #{progressPercentage}, " +
            "updated_time = NOW() " +
            "WHERE task_id = #{taskId}")
    int updateProgressPercentage(@Param("taskId") String taskId,
                                @Param("progressPercentage") BigDecimal progressPercentage);
}
