package cn.gson.financial.admin.controller;

import cn.gson.financial.admin.annotation.AdminLogin;
import cn.gson.financial.admin.annotation.AdminPermission;
import cn.gson.financial.kernel.common.Result;
import cn.gson.financial.kernel.model.entity.AccountSets;
import cn.gson.financial.kernel.model.entity.User;
import cn.gson.financial.kernel.model.entity.UserAccountSets;
import cn.gson.financial.kernel.service.AccountSetsService;
import cn.gson.financial.kernel.service.UserService;
import cn.gson.financial.kernel.service.UserAccountSetsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理员账套管理Controller
 */
@Api(tags = "管理员账套管理")
@RestController
@RequestMapping("/admin/account-sets")
@CrossOrigin
@AdminLogin
public class AdminAccountSetsController {

    @Autowired
    private AccountSetsService accountSetsService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserAccountSetsService userAccountSetsService;

    @ApiOperation("获取账套列表")
    @GetMapping("/list")
    public Result<Map<String, Object>> getAccountSetsList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer creatorId) {
        
        try {
            // 构建查询条件
            LambdaQueryWrapper<AccountSets> queryWrapper = Wrappers.lambdaQuery();
            
            // 关键字搜索（公司名称、信用代码）
            if (StringUtils.hasText(keyword)) {
                queryWrapper.and(wrapper -> wrapper
                    .like(AccountSets::getCompanyName, keyword)
                    .or().like(AccountSets::getCreditCode, keyword)
                );
            }
            
            // 按创建者筛选
            if (creatorId != null) {
                queryWrapper.eq(AccountSets::getCreatorId, creatorId);
            }
            
            // 按创建时间倒序
            queryWrapper.orderByDesc(AccountSets::getCreateDate);
            
            // 分页查询
            Page<AccountSets> accountSetsPage = new Page<>(page, size);
            IPage<AccountSets> result = accountSetsService.page(accountSetsPage, queryWrapper);
            
            // 转换为VO并添加额外信息
            List<Map<String, Object>> accountSetsList = result.getRecords().stream().map(accountSets -> {
                Map<String, Object> accountSetsMap = new HashMap<>();
                accountSetsMap.put("id", accountSets.getId());
                accountSetsMap.put("companyName", accountSets.getCompanyName());
                accountSetsMap.put("enableDate", accountSets.getEnableDate());
                accountSetsMap.put("creditCode", accountSets.getCreditCode());
                accountSetsMap.put("accountingStandards", accountSets.getAccountingStandards());
                accountSetsMap.put("address", accountSets.getAddress());
                accountSetsMap.put("industry", accountSets.getIndustry());
                accountSetsMap.put("vatType", accountSets.getVatType());
                accountSetsMap.put("createDate", accountSets.getCreateDate());
                accountSetsMap.put("creatorId", accountSets.getCreatorId());
                accountSetsMap.put("currentAccountDate", accountSets.getCurrentAccountDate());
                
                // 获取创建者信息
                if (accountSets.getCreatorId() != null) {
                    User creator = userService.getById(accountSets.getCreatorId());
                    if (creator != null) {
                        accountSetsMap.put("creatorName", creator.getRealName());
                        accountSetsMap.put("creatorMobile", creator.getMobile());
                    }
                }
                
                // 获取账套用户数量
                LambdaQueryWrapper<UserAccountSets> uasWrapper = Wrappers.lambdaQuery();
                uasWrapper.eq(UserAccountSets::getAccountSetsId, accountSets.getId());
                long userCount = userAccountSetsService.count(uasWrapper);
                accountSetsMap.put("userCount", userCount);
                
                return accountSetsMap;
            }).collect(Collectors.toList());
            
            Map<String, Object> response = new HashMap<>();
            response.put("list", accountSetsList);
            response.put("total", result.getTotal());
            response.put("current", result.getCurrent());
            response.put("size", result.getSize());
            response.put("pages", result.getPages());
            
            return Result.success(response);
            
        } catch (Exception e) {
            return Result.error("获取账套列表失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取账套详情")
    @GetMapping("/{accountSetsId}")
    @AdminPermission("account:view")
    public Result<Map<String, Object>> getAccountSetsDetail(@PathVariable Integer accountSetsId) {
        try {
            AccountSets accountSets = accountSetsService.getById(accountSetsId);
            if (accountSets == null) {
                return Result.error("账套不存在");
            }
            
            Map<String, Object> accountSetsDetail = new HashMap<>();
            accountSetsDetail.put("id", accountSets.getId());
            accountSetsDetail.put("companyName", accountSets.getCompanyName());
            accountSetsDetail.put("enableDate", accountSets.getEnableDate());
            accountSetsDetail.put("creditCode", accountSets.getCreditCode());
            accountSetsDetail.put("accountingStandards", accountSets.getAccountingStandards());
            accountSetsDetail.put("address", accountSets.getAddress());
            accountSetsDetail.put("cashierModule", accountSets.getCashierModule());
            accountSetsDetail.put("industry", accountSets.getIndustry());
            accountSetsDetail.put("fixedAssetModule", accountSets.getFixedAssetModule());
            accountSetsDetail.put("vatType", accountSets.getVatType());
            accountSetsDetail.put("voucherReviewed", accountSets.getVoucherReviewed());
            accountSetsDetail.put("createDate", accountSets.getCreateDate());
            accountSetsDetail.put("creatorId", accountSets.getCreatorId());
            accountSetsDetail.put("currentAccountDate", accountSets.getCurrentAccountDate());
            accountSetsDetail.put("encoding", accountSets.getEncoding());
            
            // 获取创建者信息
            if (accountSets.getCreatorId() != null) {
                User creator = userService.getById(accountSets.getCreatorId());
                if (creator != null) {
                    accountSetsDetail.put("creatorName", creator.getRealName());
                    accountSetsDetail.put("creatorMobile", creator.getMobile());
                }
            }
            
            // 获取账套的所有用户
            LambdaQueryWrapper<UserAccountSets> uasWrapper = Wrappers.lambdaQuery();
            uasWrapper.eq(UserAccountSets::getAccountSetsId, accountSetsId);
            List<UserAccountSets> userAccountSetsList = userAccountSetsService.list(uasWrapper);
            
            List<Map<String, Object>> userList = userAccountSetsList.stream().map(uas -> {
                Map<String, Object> userMap = new HashMap<>();
                userMap.put("userId", uas.getUserId());
                userMap.put("role", uas.getRoleType());
                
                User user = userService.getById(uas.getUserId());
                if (user != null) {
                    userMap.put("mobile", user.getMobile());
                    userMap.put("realName", user.getRealName());
                    userMap.put("nickname", user.getNickname());
                    userMap.put("email", user.getEmail());
                    userMap.put("createDate", user.getCreateDate());
                }
                
                return userMap;
            }).collect(Collectors.toList());
            
            accountSetsDetail.put("userList", userList);
            
            return Result.success(accountSetsDetail);
            
        } catch (Exception e) {
            return Result.error("获取账套详情失败: " + e.getMessage());
        }
    }

    @ApiOperation("创建账套")
    @PostMapping
    @AdminPermission("account:create")
    public Result<?> createAccountSets(@RequestBody Map<String, Object> accountSetsData) {
        try {
            String companyName = (String) accountSetsData.get("companyName");
            String enableDateStr = (String) accountSetsData.get("enableDate");
            Date enableDate = null;
            if (enableDateStr != null && !enableDateStr.isEmpty()) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                enableDate = sdf.parse(enableDateStr);
            }
            String creditCode = (String) accountSetsData.get("creditCode");
            Integer accountingStandards = (Integer) accountSetsData.get("accountingStandards");
            String address = (String) accountSetsData.get("address");
            Integer industry = (Integer) accountSetsData.get("industry");
            Integer vatType = (Integer) accountSetsData.get("vatType");
            Integer creatorId = (Integer) accountSetsData.get("creatorId");
            
            // 验证必填字段
            if (!StringUtils.hasText(companyName)) {
                return Result.error("公司名称不能为空");
            }
            if (enableDate == null) {
                return Result.error("启用日期不能为空");
            }
            if (creatorId == null) {
                return Result.error("创建者不能为空");
            }
            
            // 检查创建者是否存在
            User creator = userService.getById(creatorId);
            if (creator == null) {
                return Result.error("创建者不存在");
            }
            
            // 创建账套
            AccountSets accountSets = new AccountSets();
            accountSets.setCompanyName(companyName);
            accountSets.setEnableDate(enableDate);
            accountSets.setCreditCode(creditCode);
            accountSets.setAccountingStandards(accountingStandards != null ? accountingStandards.shortValue() : 0);
            accountSets.setAddress(address);
            accountSets.setIndustry(industry != null ? industry.shortValue() : null);
            accountSets.setVatType(vatType != null ? vatType.shortValue() : 0);
            accountSets.setCreatorId(creatorId);
            accountSets.setCreateDate(new Date());
            accountSets.setEncoding("4-2-2-2"); // 默认编码方式
            accountSets.setCashierModule((byte) 0);
            accountSets.setFixedAssetModule((byte) 0);
            accountSets.setVoucherReviewed((byte) 0);
            
            accountSetsService.save(accountSets);
            
            return Result.success("账套创建成功");
            
        } catch (Exception e) {
            return Result.error("创建账套失败: " + e.getMessage());
        }
    }

    @ApiOperation("更新账套信息")
    @PutMapping("/{accountSetsId}")
    @AdminPermission("account:edit")
    public Result<?> updateAccountSets(@PathVariable Integer accountSetsId, @RequestBody Map<String, Object> accountSetsData) {
        try {
            AccountSets accountSets = accountSetsService.getById(accountSetsId);
            if (accountSets == null) {
                return Result.error("账套不存在");
            }
            
            // 更新账套信息
            String companyName = (String) accountSetsData.get("companyName");
            String enableDateStr = (String) accountSetsData.get("enableDate");
            Date enableDate = null;
            if (enableDateStr != null && !enableDateStr.isEmpty()) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                enableDate = sdf.parse(enableDateStr);
            }
            String creditCode = (String) accountSetsData.get("creditCode");
            Integer accountingStandards = (Integer) accountSetsData.get("accountingStandards");
            String address = (String) accountSetsData.get("address");
            Integer industry = (Integer) accountSetsData.get("industry");
            Integer vatType = (Integer) accountSetsData.get("vatType");
            
            if (StringUtils.hasText(companyName)) {
                accountSets.setCompanyName(companyName);
            }
            if (enableDate != null) {
                accountSets.setEnableDate(enableDate);
            }
            if (StringUtils.hasText(creditCode)) {
                accountSets.setCreditCode(creditCode);
            }
            if (accountingStandards != null) {
                accountSets.setAccountingStandards(accountingStandards.shortValue());
            }
            if (StringUtils.hasText(address)) {
                accountSets.setAddress(address);
            }
            if (industry != null) {
                accountSets.setIndustry(industry.shortValue());
            }
            if (vatType != null) {
                accountSets.setVatType(vatType.shortValue());
            }
            
            accountSetsService.updateById(accountSets);
            
            return Result.success("账套信息更新成功");
            
        } catch (Exception e) {
            return Result.error("更新账套信息失败: " + e.getMessage());
        }
    }
}
