package cn.gson.financial.service;

import cn.gson.financial.kernel.model.entity.BatchImportTask;
import cn.gson.financial.kernel.model.entity.BatchImportDetail;
import cn.gson.financial.kernel.model.entity.BankReceipts;
import cn.gson.financial.kernel.model.entity.Bill;
import cn.gson.financial.kernel.model.entity.AccountSets;
import cn.gson.financial.kernel.model.mapper.BatchImportTaskMapper;
import cn.gson.financial.kernel.model.mapper.BatchImportDetailMapper;
import cn.gson.financial.kernel.service.BankReceiptsService;
import cn.gson.financial.kernel.service.BillService;
import cn.gson.financial.kernel.service.AiService;
import cn.gson.financial.kernel.service.AccountSetsService;
import cn.gson.financial.kernel.model.entity.FieldMappingTemplate;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;


import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 异步批量处理服务
 * 负责文件处理和OCR识别的异步执行
 */
@Service
@Slf4j
public class AsyncBatchProcessService {

    @Autowired
    private BatchImportTaskMapper batchImportTaskMapper;

    @Autowired
    private BatchImportDetailMapper batchImportDetailMapper;

    @Autowired
    private PdfProcessService pdfProcessService;

    @Autowired
    private BatchOcrService batchOcrService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private BankReceiptsService bankReceiptsService;

    @Autowired
    private BillService billService;

    @Autowired
    private AiService aiService;

    @Autowired
    private FieldMappingTemplateService templateService;

    @Autowired(required = false)
    private SmartFieldMappingService smartFieldMappingService;

    @Autowired
    private StandardInvoiceTemplateService standardInvoiceTemplateService;

    @Autowired
    private AccountSetsService accountSetsService;

    @Autowired
    private OcrService ocrService;

    /**
     * 异步处理文件上传任务
     * @param taskId 任务ID
     * @param files 文件数组
     * @param type 导入类型
     * @param receiptsPerPage 每页回单数
     * @param accountSetsId 账套ID
     * @param userId 用户ID
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> processUploadedFiles(String taskId,
                                                       org.springframework.web.multipart.MultipartFile[] files,
                                                       String type,
                                                       Integer receiptsPerPage,
                                                       String accountSetsId,
                                                       Integer userId) {
        return processUploadedFilesInternal(taskId, files, null, type, receiptsPerPage, accountSetsId, userId);
    }

    /**
     * 异步处理上传的文件（File版本）
     * @param taskId 任务ID
     * @param tempFiles 临时文件列表
     * @param type 导入类型
     * @param receiptsPerPage 每页回单数量
     * @param accountSetsId 账套ID
     * @param userId 用户ID
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> processUploadedFiles(String taskId,
                                                       List<java.io.File> tempFiles,
                                                       String type,
                                                       Integer receiptsPerPage,
                                                       String accountSetsId,
                                                       Integer userId) {
        return processUploadedFilesInternal(taskId, null, tempFiles, type, receiptsPerPage, accountSetsId, userId);
    }

    /**
     * 内部处理方法
     */
    private CompletableFuture<Void> processUploadedFilesInternal(String taskId,
                                                               org.springframework.web.multipart.MultipartFile[] multipartFiles,
                                                               List<java.io.File> tempFiles,
                                                               String type,
                                                               Integer receiptsPerPage,
                                                               String accountSetsId,
                                                               Integer userId) {
        try {
            log.info("开始异步处理上传任务: {}", taskId);

            // 更新任务状态为处理中
            updateTaskStatus(taskId, BatchImportTask.STATUS_PROCESSING, "正在处理文件...");
            sendProgressUpdate(taskId, 0, "开始处理文件");

            // 处理文件上传
            String folder = type.equals(BatchImportTask.TYPE_BANK_RECEIPT) ? "batch-bank-receipts" : "batch-invoices";
            List<Map<String, Object>> processResults;
            int fileCount;

            if (multipartFiles != null) {
                processResults = pdfProcessService.processUploadedFiles(multipartFiles, folder, receiptsPerPage);
                fileCount = multipartFiles.length;
            } else if (tempFiles != null) {
                processResults = pdfProcessService.processUploadedTempFiles(tempFiles, folder, receiptsPerPage);
                fileCount = tempFiles.size();
                // 处理完成后清理临时文件
                for (java.io.File tempFile : tempFiles) {
                    if (tempFile.exists()) {
                        tempFile.delete();
                    }
                }
            } else {
                throw new RuntimeException("没有提供文件进行处理");
            }

            // 创建明细记录
            int totalImages = createDetailRecords(taskId, processResults, accountSetsId);

            // 更新任务信息
            updateTaskProgress(taskId, fileCount, totalImages, 50, "文件处理完成");

            // 自动开始OCR识别
            startOcrRecognition(taskId, accountSetsId, userId);

            log.info("文件处理完成，任务ID: {}, 总图片数: {}", taskId, totalImages);
            
        } catch (Exception e) {
            log.error("异步处理文件失败，任务ID: {}", taskId, e);
            updateTaskStatus(taskId, BatchImportTask.STATUS_FAILED, "文件处理失败: " + e.getMessage());
            sendProgressUpdate(taskId, -1, "处理失败: " + e.getMessage());
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 异步开始OCR识别
     * @param taskId 任务ID
     * @param accountSetsId 账套ID
     * @param userId 用户ID
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> startOcrRecognition(String taskId, String accountSetsId, Integer userId) {
        try {
            log.info("开始异步OCR识别，任务ID: {}", taskId);
            
            // 获取任务信息
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask>()
                    .eq(BatchImportTask::getTaskId, taskId)
                    .eq(BatchImportTask::getAccountSetsId, Integer.parseInt(accountSetsId))
            );
            if (task == null) {
                throw new RuntimeException("任务不存在");
            }
            
            // 获取待识别的明细
            List<BatchImportDetail> details = batchImportDetailMapper.selectByTaskId(taskId);
            if (details.isEmpty()) {
                throw new RuntimeException("没有找到待识别的图片");
            }
            
            // 更新任务状态
            updateTaskStatus(taskId, BatchImportTask.STATUS_RECOGNIZING, "正在进行OCR识别...");
            sendProgressUpdate(taskId, 60, "开始OCR识别");
            
            // 执行批量识别
            List<Integer> detailIds = details.stream()
                .map(BatchImportDetail::getId)
                .collect(java.util.stream.Collectors.toList());
            
            CompletableFuture<Map<String, Object>> recognitionFuture;
            if (BatchImportTask.TYPE_BANK_RECEIPT.equals(task.getImportType())) {
                recognitionFuture = batchOcrService.batchRecognizeBankReceipts(taskId, detailIds, userId);
            } else {
                recognitionFuture = batchOcrService.batchRecognizeInvoices(taskId, detailIds, userId);
            }
            
            // 等待识别完成
            Map<String, Object> result = recognitionFuture.get();
            
            if ("COMPLETED".equals(result.get("status"))) {
                // 更新任务状态为PREVIEWING，设置进度为90%（识别完成但还需要预览确认）
                updateTaskStatusWithProgress(taskId, BatchImportTask.STATUS_PREVIEWING, "识别完成，可以预览结果", 90);
                sendProgressUpdate(taskId, 90, "识别完成，待预览");

                // 发送完成通知
                sendCompletionNotification(taskId, (Integer) result.get("successCount"), (Integer) result.get("failedCount"));

            } else {
                updateTaskStatus(taskId, BatchImportTask.STATUS_FAILED, (String) result.get("error"));
                sendProgressUpdate(taskId, -1, "识别失败: " + result.get("error"));
            }
            
            log.info("OCR识别完成，任务ID: {}", taskId);
            
        } catch (Exception e) {
            log.error("异步OCR识别失败，任务ID: {}", taskId, e);
            updateTaskStatus(taskId, BatchImportTask.STATUS_FAILED, "OCR识别失败: " + e.getMessage());
            sendProgressUpdate(taskId, -1, "识别失败: " + e.getMessage());
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 创建明细记录
     */
    private int createDetailRecords(String taskId, List<Map<String, Object>> processResults, String accountSetsId) {
        int totalImages = 0;

        log.info("开始创建明细记录，任务ID: {}, 处理结果数量: {}", taskId, processResults.size());

        for (Map<String, Object> fileResult : processResults) {
            log.info("处理文件结果: {}", fileResult);

            // 检查处理是否成功 - 兼容两种状态标识
            Boolean success = (Boolean) fileResult.get("success");
            String status = (String) fileResult.get("status");
            log.info("文件处理状态 - success: {}, status: {}", success, status);

            if ((success != null && success) || "SUCCESS".equals(status)) {
                String fileName = (String) fileResult.get("fileName");
                String fileType = (String) fileResult.get("fileType");
                String originalFileUrl = (String) fileResult.get("originalFileUrl");
                Long fileSize = ((Number) fileResult.get("fileSize")).longValue();
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> images = (List<Map<String, Object>>) fileResult.get("images");
                log.info("文件包含图片数量: {}", images != null ? images.size() : 0);

                if (images != null) {
                    for (Map<String, Object> imageInfo : images) {
                    log.info("处理图片信息: {}", imageInfo);
                    BatchImportDetail detail = new BatchImportDetail();
                    detail.setTaskId(taskId);
                    detail.setFileName(fileName);
                    detail.setFileUrl(originalFileUrl);
                    detail.setFileType(fileType);
                    detail.setFileSize(fileSize);
                    detail.setPageNumber((Integer) imageInfo.get("pageNumber"));
                    detail.setImageUrl((String) imageInfo.get("imageUrl"));
                    detail.setImageWidth((Integer) imageInfo.get("width"));
                    detail.setImageHeight((Integer) imageInfo.get("height"));
                    detail.setStatus(BatchImportDetail.STATUS_PENDING);
                    detail.setRetryCount(0);
                    detail.setCreatedTime(LocalDateTime.now());
                    detail.setAccountSetsId(Integer.parseInt(accountSetsId));
                    
                    batchImportDetailMapper.insert(detail);
                    totalImages++;
                    }
                }
            }
        }
        
        return totalImages;
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(String taskId, String status, String message) {
        batchImportTaskMapper.updateStatus(taskId, status, message);
    }

    /**
     * 更新任务状态和进度
     */
    private void updateTaskStatusWithProgress(String taskId, String status, String message, int progress) {
        batchImportTaskMapper.updateStatus(taskId, status, message);
        // 只更新进度百分比，不影响其他计数字段
        batchImportTaskMapper.updateProgressPercentage(taskId, BigDecimal.valueOf(progress));
    }

    /**
     * 更新任务进度
     */
    private void updateTaskProgress(String taskId, int totalFiles, int totalImages, int progress, String message) {
        batchImportTaskMapper.updateProgress(taskId, totalFiles, totalImages, 0, 0, BigDecimal.valueOf(progress));
    }

    /**
     * 发送进度更新到前端
     */
    private void sendProgressUpdate(String taskId, int progress, String message) {
        try {
            Map<String, Object> update = new HashMap<>();
            update.put("taskId", taskId);
            update.put("progress", progress);
            update.put("message", message);
            update.put("timestamp", System.currentTimeMillis());
            
            // 发送到特定任务的订阅者
            messagingTemplate.convertAndSend("/topic/batch-progress/" + taskId, update);
            
        } catch (Exception e) {
            log.warn("发送进度更新失败，任务ID: {}", taskId, e);
        }
    }

    /**
     * 发送完成通知
     */
    private void sendCompletionNotification(String taskId, int successCount, int failedCount) {
        try {
            Map<String, Object> notification = new HashMap<>();
            notification.put("taskId", taskId);
            notification.put("type", "completion");
            notification.put("successCount", successCount);
            notification.put("failedCount", failedCount);
            notification.put("message", String.format("识别完成！成功: %d, 失败: %d", successCount, failedCount));
            notification.put("timestamp", System.currentTimeMillis());
            
            messagingTemplate.convertAndSend("/topic/batch-progress/" + taskId, notification);
            
        } catch (Exception e) {
            log.warn("发送完成通知失败，任务ID: {}", taskId, e);
        }
    }

    /**
     * 异步批量保存到业务表
     */
    @Async
    public CompletableFuture<Void> batchSaveToBusinessTables(String taskId, List<Integer> selectedIds, String importType, Integer accountSetsId) {
        log.info("开始异步批量保存，任务ID: {}, 总数: {}", taskId, selectedIds.size());

        try {
            // 获取任务信息，包括创建用户ID
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new LambdaQueryWrapper<BatchImportTask>().eq(BatchImportTask::getTaskId, taskId));
            Integer createUser = task != null ? task.getCreateUser() : null;

            // 确保任务状态为保存中
            batchImportTaskMapper.updateStatus(taskId, BatchImportTask.STATUS_SAVING, null);
            sendProgressUpdate(taskId, 0, "开始批量保存...");

            int successCount = 0;
            int failedCount = 0;
            int totalCount = selectedIds.size();

            // 分批处理，每批5个（减少批次大小以提供更频繁的进度更新）
            int batchSize = 5;
            for (int i = 0; i < selectedIds.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, selectedIds.size());
                List<Integer> batchIds = selectedIds.subList(i, endIndex);

                // 处理当前批次
                for (Integer detailId : batchIds) {
                    try {
                        BatchImportDetail detail = batchImportDetailMapper.selectById(detailId);
                        if (detail != null && detail.getAccountSetsId().equals(accountSetsId) &&
                            (detail.getFinalData() != null || detail.getParsedData() != null)) {

                            // 确定实际的保存类型（优先使用记录的实际类型，否则使用任务类型）
                            String actualType = getActualTypeFromDetail(detail, importType);

                            // 根据实际类型保存到对应的业务表
                            if (BatchImportTask.TYPE_BANK_RECEIPT.equals(actualType)) {
                                saveBankReceiptFromDetail(detail, taskId, createUser);
                                successCount++;
                            } else if (BatchImportTask.TYPE_INVOICE.equals(actualType)) {
                                saveBillFromDetail(detail, taskId, createUser);
                                successCount++;
                            } else {
                                log.warn("不支持的导入类型: {}", actualType);
                                failedCount++;
                            }
                        } else {
                            log.warn("明细数据为空或无解析数据，ID: {}", detailId);
                            failedCount++;
                        }
                    } catch (Exception e) {
                        log.error("保存明细失败，ID: {}", detailId, e);
                        failedCount++;
                    }
                }

                // 发送进度更新
                int processed = i + batchIds.size();
                int progress = (int) ((double) processed / totalCount * 100);
                sendProgressUpdate(taskId, progress, String.format("正在保存... %d/%d", processed, totalCount));

                // 短暂休息，避免数据库压力过大
                Thread.sleep(200);
            }

            // 更新最终任务状态
            if (failedCount == 0) {
                updateTaskStatusWithProgress(taskId, BatchImportTask.STATUS_COMPLETED, null, 100);
                sendProgressUpdate(taskId, 100, String.format("保存完成！成功: %d", successCount));
            } else {
                String errorMessage = String.format("部分保存失败，成功: %d, 失败: %d。失败原因可能包括：数据重复、格式错误等",
                    successCount, failedCount);
                batchImportTaskMapper.updateStatus(taskId, BatchImportTask.STATUS_PARTIAL_SUCCESS, errorMessage);
                sendProgressUpdate(taskId, 90, String.format("保存完成，成功: %d, 失败: %d", successCount, failedCount));
            }

            // 发送完成通知
            sendSaveCompletionNotification(taskId, successCount, failedCount);

            log.info("批量保存完成，任务ID: {}, 成功: {}, 失败: {}", taskId, successCount, failedCount);

        } catch (Exception e) {
            log.error("异步批量保存失败，任务ID: {}", taskId, e);
            batchImportTaskMapper.updateStatus(taskId, BatchImportTask.STATUS_FAILED, "保存失败: " + e.getMessage());
            sendProgressUpdate(taskId, -1, "保存失败: " + e.getMessage());
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 发送保存完成通知
     */
    private void sendSaveCompletionNotification(String taskId, int successCount, int failedCount) {
        try {
            Map<String, Object> notification = new HashMap<>();
            notification.put("taskId", taskId);
            notification.put("type", "save_completion");
            notification.put("successCount", successCount);
            notification.put("failedCount", failedCount);
            notification.put("message", String.format("保存完成！成功: %d, 失败: %d", successCount, failedCount));
            notification.put("timestamp", System.currentTimeMillis());

            messagingTemplate.convertAndSend("/topic/batch-progress/" + taskId, notification);

        } catch (Exception e) {
            log.warn("发送保存完成通知失败，任务ID: {}", taskId, e);
        }
    }

    /**
     * 从明细记录中获取实际的识别类型
     * @param detail 批量导入明细
     * @param defaultType 默认类型（任务类型）
     * @return 实际的识别类型
     */
    private String getActualTypeFromDetail(BatchImportDetail detail, String defaultType) {
        try {
            // 检查parsedData中是否记录了实际类型
            String parsedData = detail.getParsedData();
            if (parsedData != null && !parsedData.trim().isEmpty()) {
                JSONObject parsedJson = JSON.parseObject(parsedData);
                if (parsedJson != null && parsedJson.containsKey("actualType")) {
                    String actualType = parsedJson.getString("actualType");
                    if (BatchImportTask.TYPE_BANK_RECEIPT.equals(actualType) ||
                        BatchImportTask.TYPE_INVOICE.equals(actualType)) {
                        log.info("明细ID: {} 使用实际识别类型: {}", detail.getId(), actualType);
                        return actualType;
                    }
                }
            }

            // 检查finalData中是否记录了实际类型
            String finalData = detail.getFinalData();
            if (finalData != null && !finalData.trim().isEmpty()) {
                JSONObject finalJson = JSON.parseObject(finalData);
                if (finalJson != null && finalJson.containsKey("actualType")) {
                    String actualType = finalJson.getString("actualType");
                    if (BatchImportTask.TYPE_BANK_RECEIPT.equals(actualType) ||
                        BatchImportTask.TYPE_INVOICE.equals(actualType)) {
                        log.info("明细ID: {} 使用最终数据中的实际识别类型: {}", detail.getId(), actualType);
                        return actualType;
                    }
                }
            }

            // 如果没有找到实际类型，使用默认类型
            log.info("明细ID: {} 使用默认识别类型: {}", detail.getId(), defaultType);
            return defaultType;

        } catch (Exception e) {
            log.warn("解析明细ID: {} 的实际类型失败，使用默认类型: {}", detail.getId(), defaultType, e);
            return defaultType;
        }
    }

    /**
     * 获取字段值，优先从finalData获取，如果没有则从rawOcrData的多个可能字段名中获取
     */
    private String getFieldValue(JSONObject finalData, JSONObject rawOcrData, String... fieldNames) {
        if (finalData == null) {
            return null;
        }

        // 优先从finalData的直接字段获取
        for (String fieldName : fieldNames) {
            String value = finalData.getString(fieldName);
            if (value != null && !value.trim().isEmpty()) {
                return value.trim();
            }
        }

        // 如果finalData中没有，则从rawOcrData中查找
        if (rawOcrData != null) {
            for (String fieldName : fieldNames) {
                String value = rawOcrData.getString(fieldName);
                if (value != null && !value.trim().isEmpty()) {
                    return value.trim();
                }
            }
        }

        return null;
    }

    /**
     * 从批量导入明细保存银行回单
     */
    private void saveBankReceiptFromDetail(BatchImportDetail detail, String taskId, Integer createUser) {
        try {
            // 解析数据，优先使用final_data，如果没有则使用parsed_data
            String dataStr = detail.getFinalData() != null ? detail.getFinalData() : detail.getParsedData();
            log.info("批量导入银行回单，明细ID: {}, 数据来源: {}, 数据长度: {}",
                detail.getId(),
                detail.getFinalData() != null ? "final_data" : "parsed_data",
                dataStr != null ? dataStr.length() : 0);

            if (dataStr == null || dataStr.trim().isEmpty()) {
                log.error("明细ID: {} 没有可用的解析数据，final_data: {}, parsed_data: {}",
                    detail.getId(), detail.getFinalData(), detail.getParsedData());
                throw new RuntimeException("没有可用的解析数据");
            }

            JSONObject finalData = JSON.parseObject(dataStr);
            if (finalData == null) {
                log.error("解析数据失败，数据格式不正确，原始数据: {}", dataStr);
                throw new RuntimeException("解析数据失败，数据格式不正确");
            }

            log.info("批量保存银行回单，直接使用已映射的数据，明细ID: {}, 字段数: {}",
                detail.getId(), finalData.size());

            // 检查重复数据
            String duplicateInfo = checkBankReceiptDuplicate(finalData, detail.getAccountSetsId());
            if (duplicateInfo != null) {
                log.warn("检测到重复的银行回单，跳过保存。明细ID: {}, 重复信息: {}",
                    detail.getId(), duplicateInfo);
                // 抛出异常，让上层处理并记录到错误信息中
                throw new RuntimeException("银行回单重复：" + duplicateInfo + "，已跳过保存");
            }

            // 创建银行回单对象
            BankReceipts bankReceipt = new BankReceipts();

            // 获取rawOcrData用于字段映射
            JSONObject rawOcrData = finalData.getJSONObject("rawOcrData");

            // 设置基本信息，优先从直接字段获取，如果没有则从rawOcrData获取
            bankReceipt.setReceiptTitle(getFieldValue(finalData, rawOcrData, "receipt_title", "title", "标题"));

            // 处理金额字段
            Double amount = null;
            try {
                amount = finalData.getDouble("amount");
            } catch (Exception e) {
                // 如果getDouble失败，尝试作为字符串处理
                String amountStr = finalData.getString("amount");
                if (amountStr != null) {
                    try {
                        // 清理金额格式，移除引号、CNY前缀和逗号
                        amountStr = amountStr.replace("\"", "").replace("CNY", "").replace("¥", "").replace(",", "").trim();
                        amount = Double.parseDouble(amountStr);
                    } catch (NumberFormatException ex) {
                        log.warn("解析金额失败: {}", amountStr, e);
                    }
                }
            }

            if (amount == null && rawOcrData != null) {
                String amountStr = rawOcrData.getString("金额");
                if (amountStr != null) {
                    try {
                        // 清理金额格式，移除CNY前缀和逗号
                        amountStr = amountStr.replace("CNY", "").replace("¥", "").replace(",", "").trim();
                        amount = Double.parseDouble(amountStr);
                    } catch (NumberFormatException e) {
                        log.warn("无法解析金额字段: {}", amountStr);
                    }
                }
            }
            bankReceipt.setAmount(amount);

            // 设置收款人信息
            bankReceipt.setPayeeName(getFieldValue(finalData, rawOcrData, "payee_name", "payeeName", "收款人名称", "收款人", "收款方"));
            bankReceipt.setPayeeAccount(getFieldValue(finalData, rawOcrData, "payee_account", "payeeAccount", "收款人账号", "收款账号"));
            bankReceipt.setPayeeBank(getFieldValue(finalData, rawOcrData, "payee_bank", "payeeBank", "收款人开户行", "开户行名称"));

            // 设置付款人信息
            bankReceipt.setPayerName(getFieldValue(finalData, rawOcrData, "payer_name", "payerName", "付款人名称", "付款人", "付款方"));
            bankReceipt.setPayerAccount(getFieldValue(finalData, rawOcrData, "payer_account", "payerAccount", "付款人账号", "付款账号"));
            bankReceipt.setPayerBank(getFieldValue(finalData, rawOcrData, "payer_bank", "payerBank", "付款人开户行"));

            // 设置其他信息
            bankReceipt.setSerialNumber(getFieldValue(finalData, rawOcrData, "serial_number", "serialNumber", "会计流水号", "流水号", "回单编号"));
            bankReceipt.setTransactionInstitution(getFieldValue(finalData, rawOcrData, "transaction_institution", "institution", "机构", "交易机构"));
            bankReceipt.setAmountInWords(getFieldValue(finalData, rawOcrData, "amount_in_words", "amountInWords", "金额大写"));

            log.info("批量保存银行回单字段映射完成，明细ID: {}, 收款人: {}, 付款人: {}, 金额: {}",
                detail.getId(), bankReceipt.getPayeeName(), bankReceipt.getPayerName(), bankReceipt.getAmount());

            // 处理必填字段：摘要
            String summary = finalData.getString("summary");
            if (summary == null || summary.trim().isEmpty()) {
                // 如果没有摘要，根据回单标题生成默认摘要
                String receiptTitle = finalData.getString("receipt_title");
                if (receiptTitle != null && !receiptTitle.trim().isEmpty()) {
                    summary = receiptTitle;
                } else {
                    summary = "银行回单";
                }
            }
            bankReceipt.setSummary(summary);

            // 处理必填字段：张数（默认1）
            Integer quantity = finalData.getInteger("quantity");
            if (quantity == null || quantity <= 0) {
                quantity = 1;
            }
            bankReceipt.setReceiptNum(quantity);

            // 处理必填字段：类型
            String type = finalData.getString("type");
            if (type == null || type.trim().isEmpty()) {
                // 如果AI没有判断出类型，根据回单标题进行简单判断
                String receiptTitle = finalData.getString("receipt_title");
                if (receiptTitle != null) {
                    if (receiptTitle.contains("收入") || receiptTitle.contains("转入") || receiptTitle.contains("存入") || receiptTitle.contains("贷方")) {
                        type = "收入";
                    } else if (receiptTitle.contains("支出") || receiptTitle.contains("转出") || receiptTitle.contains("支付") || receiptTitle.contains("借方")) {
                        type = "支出";
                    } else {
                        type = "支出"; // 默认为支出
                    }
                } else {
                    type = "支出"; // 默认为支出
                }
            }
            bankReceipt.setType(type);

            // 处理日期字段
            String receiptsDateStr = finalData.getString("receipts_date");
            if (receiptsDateStr != null && !receiptsDateStr.isEmpty()) {
                try {
                    // 尝试多种日期格式
                    java.text.SimpleDateFormat[] formats = {
                        new java.text.SimpleDateFormat("yyyy-MM-dd"),
                        new java.text.SimpleDateFormat("yyyy/MM/dd"),
                        new java.text.SimpleDateFormat("yyyy年MM月dd日")
                    };

                    for (java.text.SimpleDateFormat format : formats) {
                        try {
                            bankReceipt.setReceiptsDate(format.parse(receiptsDateStr));
                            break;
                        } catch (java.text.ParseException ignored) {
                            // 继续尝试下一种格式
                        }
                    }

                    if (bankReceipt.getReceiptsDate() == null) {
                        log.warn("无法解析日期格式: {}, 使用当前日期", receiptsDateStr);
                        bankReceipt.setReceiptsDate(new Date());
                    }
                } catch (Exception e) {
                    log.warn("解析日期失败: {}, 使用当前日期", receiptsDateStr, e);
                    bankReceipt.setReceiptsDate(new Date());
                }
            } else {
                bankReceipt.setReceiptsDate(new Date());
            }

            // 处理转账日期（必填字段）
            String transferDateStr = finalData.getString("transfer_date");
            if (transferDateStr != null && !transferDateStr.isEmpty()) {
                try {
                    java.text.SimpleDateFormat[] formats = {
                        new java.text.SimpleDateFormat("yyyy-MM-dd"),
                        new java.text.SimpleDateFormat("yyyy/MM/dd"),
                        new java.text.SimpleDateFormat("yyyy年MM月dd日")
                    };

                    for (java.text.SimpleDateFormat format : formats) {
                        try {
                            bankReceipt.setTransferDate(format.parse(transferDateStr));
                            break;
                        } catch (java.text.ParseException ignored) {
                            // 继续尝试下一种格式
                        }
                    }

                    if (bankReceipt.getTransferDate() == null) {
                        log.warn("无法解析转账日期格式: {}, 使用回单日期", transferDateStr);
                        bankReceipt.setTransferDate(bankReceipt.getReceiptsDate());
                    }
                } catch (Exception e) {
                    log.warn("解析转账日期失败: {}, 使用回单日期", transferDateStr, e);
                    bankReceipt.setTransferDate(bankReceipt.getReceiptsDate());
                }
            } else {
                // 如果没有转账日期，使用回单日期作为默认值
                log.info("转账日期为空，使用回单日期作为默认值");
                bankReceipt.setTransferDate(bankReceipt.getReceiptsDate());
            }

            // 设置图片路径
            bankReceipt.setFilePath(detail.getImageUrl());

            // 设置OCR识别信息（使用易读文本格式保存）
            String ocrRecognitionInfo = finalData.getString("ocrRecognitionInfo");
            if (ocrRecognitionInfo == null || ocrRecognitionInfo.trim().isEmpty()) {
                // 如果没有格式化的OCR信息，尝试从原始数据生成
                try {
                    JSONObject originalData = JSON.parseObject(detail.getRecognitionResult());
                    @SuppressWarnings("unchecked")
                    Map<String, String> originalRawOcrData = (Map<String, String>) originalData.get("rawOcrData");
                    if (originalRawOcrData != null && ocrService != null) {
                        ocrRecognitionInfo = ocrService.formatOcrDataToReadableText(originalRawOcrData, "BANK_RECEIPT");
                    } else {
                        ocrRecognitionInfo = detail.getRecognitionResult();
                    }
                } catch (Exception e) {
                    log.warn("解析OCR原始数据失败，使用原始结果", e);
                    ocrRecognitionInfo = detail.getRecognitionResult();
                }
            }
            bankReceipt.setOcrRecognitionInfo(ocrRecognitionInfo);

            // 设置备注信息（保持用户可编辑的备注功能）
            String userRemark = finalData.getString("remark");
            if (userRemark != null && !userRemark.trim().isEmpty()) {
                bankReceipt.setRemark(userRemark);
            }

            // 设置账套ID
            bankReceipt.setAccountSetsId(detail.getAccountSetsId());

            // 设置年月期间（从系统当前期间获取）
            java.time.LocalDate now = java.time.LocalDate.now();
            bankReceipt.setReceiptsYear(now.getYear());
            bankReceipt.setReceiptsMonth(now.getMonthValue());

            // 设置创建信息
            bankReceipt.setCreateDate(new Date());

            // 从任务信息中获取创建用户ID
            BatchImportTask task = batchImportTaskMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<BatchImportTask>()
                    .eq(BatchImportTask::getTaskId, taskId)
            );
            if (task != null) {
                bankReceipt.setCreateMember(task.getCreateUser());
            } else {
                bankReceipt.setCreateMember(1); // 默认用户ID
            }

            // 保存到数据库，使用完整的服务方法
            bankReceiptsService.save(detail.getAccountSetsId(), bankReceipt, null);

            log.info("银行回单保存成功，明细ID: {}, 回单编号: {}", detail.getId(), bankReceipt.getReceiptsNo());

        } catch (Exception e) {
            log.error("保存银行回单失败，明细ID: {}", detail.getId(), e);
            throw e;
        }
    }



    /**
     * 从批量导入明细保存发票
     */
    private void saveBillFromDetail(BatchImportDetail detail, String taskId, Integer createUser) {
        try {
            // 解析数据，优先使用final_data，如果没有则使用parsed_data
            String dataStr = detail.getFinalData() != null ? detail.getFinalData() : detail.getParsedData();
            log.info("批量导入发票，明细ID: {}, 数据来源: {}, 数据长度: {}",
                detail.getId(),
                detail.getFinalData() != null ? "final_data" : "parsed_data",
                dataStr != null ? dataStr.length() : 0);

            if (dataStr == null || dataStr.trim().isEmpty()) {
                log.error("明细ID: {} 没有可用的解析数据，final_data: {}, parsed_data: {}",
                    detail.getId(), detail.getFinalData(), detail.getParsedData());
                throw new RuntimeException("没有可用的解析数据");
            }

            JSONObject ocrData = JSON.parseObject(dataStr);
            if (ocrData == null) {
                log.error("解析OCR数据失败，数据格式不正确，原始数据: {}", dataStr);
                throw new RuntimeException("解析OCR数据失败，数据格式不正确");
            }

            // 使用模板或LLM智能映射字段
            JSONObject finalData = smartMapBillFields(ocrData, detail.getAccountSetsId(), createUser);

            // 检查重复数据
            String duplicateInfo = checkBillDuplicate(finalData, detail.getAccountSetsId());
            if (duplicateInfo != null) {
                log.warn("检测到重复的票据，跳过保存。明细ID: {}, 重复信息: {}",
                    detail.getId(), duplicateInfo);
                // 抛出异常，让上层处理并记录到错误信息中
                throw new RuntimeException("发票重复：" + duplicateInfo + "，已跳过保存");
            }

            // 创建发票对象
            Bill bill = new Bill();

            // 设置基本信息
            bill.setType(finalData.getString("type") != null ? finalData.getString("type") : "发票");

            // 处理金额字段，确保不为null
            Double amount = finalData.getDouble("amount");
            if (amount == null) {
                // 从原始OCR识别结果中提取金额
                try {
                    JSONObject originalOcrData = JSON.parseObject(detail.getRecognitionResult());
                    @SuppressWarnings("unchecked")
                    Map<String, String> rawData = (Map<String, String>) originalOcrData.get("rawOcrData");
                    if (rawData != null) {
                        // 按优先级提取金额字段
                        String amountStr = rawData.get("价税合计(小写)");
                        if (amountStr == null) {
                            amountStr = rawData.get("价税合计（小写）");
                        }
                        if (amountStr == null) {
                            amountStr = rawData.get("合计金额");
                        }
                        if (amountStr == null) {
                            amountStr = rawData.get("总金额");
                        }
                        if (amountStr == null) {
                            amountStr = rawData.get("小写金额");
                        }

                        if (amountStr != null && !amountStr.trim().isEmpty()) {
                            // 去除货币符号和空格，提取数字
                            amountStr = amountStr.replaceAll("[￥¥$,，\\s]", "").trim();
                            try {
                                amount = Double.parseDouble(amountStr);
                                log.info("从OCR原始数据提取金额: {} -> {}, 明细ID: {}", rawData.get("价税合计(小写)"), amount, detail.getId());
                            } catch (NumberFormatException e) {
                                log.warn("无法解析金额字段: {}, 明细ID: {}", amountStr, detail.getId());
                                amount = 0.0;
                            }
                        } else {
                            log.warn("发票金额字段为空，明细ID: {}, 设置默认值0.0", detail.getId());
                            amount = 0.0;
                        }
                    } else {
                        log.warn("OCR原始数据为空，明细ID: {}, 设置默认值0.0", detail.getId());
                        amount = 0.0;
                    }
                } catch (Exception e) {
                    log.warn("解析OCR原始数据失败，明细ID: {}, 设置默认值0.0", detail.getId(), e);
                    amount = 0.0;
                }
            }
            bill.setAmount(amount);

            // 从原始OCR数据中提取其他字段（如果finalData中没有的话）
            JSONObject originalOcrData = null;
            Map<String, String> rawData = null;
            try {
                originalOcrData = JSON.parseObject(detail.getRecognitionResult());
                @SuppressWarnings("unchecked")
                Map<String, String> tempRawData = (Map<String, String>) originalOcrData.get("rawOcrData");
                rawData = tempRawData;
            } catch (Exception e) {
                log.warn("解析OCR原始数据失败，明细ID: {}", detail.getId(), e);
            }

            // 重新设置发票类型（优先从OCR原始数据中提取）
            String billType = null;
            if (rawData != null) {
                billType = rawData.get("发票类型");
                if (billType == null) {
                    billType = rawData.get("发票名称");
                }
                if (billType == null) {
                    billType = rawData.get("票据类型");
                }
                if (billType != null && !billType.trim().isEmpty()) {
                    bill.setType(billType);
                    log.info("从OCR原始数据提取发票类型: {}, 明细ID: {}", billType, detail.getId());
                } else {
                    log.info("OCR原始数据中未找到发票类型，使用字段映射结果: {}, 明细ID: {}", bill.getType(), detail.getId());
                }
            }

            // 设置开票方和收票方信息
            String issuer = finalData.getString("issuer");
            if (issuer == null || issuer.trim().isEmpty()) {
                issuer = finalData.getString("sellerName");
            }
            if (issuer == null || issuer.trim().isEmpty() && rawData != null) {
                issuer = rawData.get("销售方名称");
                if (issuer == null) {
                    issuer = rawData.get("开票方名称");
                }
            }
            if (issuer == null || issuer.trim().isEmpty()) {
                issuer = "未知开票方"; // 设置默认值，确保不为空
            }
            bill.setIssuer(issuer);

            String recipient = finalData.getString("recipient");
            if (recipient == null) {
                recipient = finalData.getString("buyerName");
            }
            if (recipient == null && rawData != null) {
                recipient = rawData.get("购买方名称");
            }
            bill.setRecipient(recipient);

            // 设置发票号码
            String invoiceNumber = finalData.getString("invoice_number");
            if (invoiceNumber == null && rawData != null) {
                invoiceNumber = rawData.get("发票号码");
                if (invoiceNumber == null) {
                    invoiceNumber = rawData.get("打印发票号码");
                }
            }
            bill.setInvoiceNumber(invoiceNumber);

            // 设置税率
            if (rawData != null) {
                String taxRateStr = rawData.get("税率");
                if (taxRateStr != null && !taxRateStr.trim().isEmpty()) {
                    try {
                        // 去除百分号
                        taxRateStr = taxRateStr.replace("%", "").trim();
                        BigDecimal taxRate = new BigDecimal(taxRateStr).divide(new BigDecimal("100"));
                        bill.setTaxRate(taxRate);
                    } catch (Exception e) {
                        log.warn("解析税率失败: {}, 明细ID: {}", taxRateStr, detail.getId());
                    }
                }
            }

            // 设置税额
            if (rawData != null) {
                String taxAmountStr = rawData.get("合计税额");
                if (taxAmountStr == null) {
                    taxAmountStr = rawData.get("税额");
                }
                if (taxAmountStr != null && !taxAmountStr.trim().isEmpty()) {
                    try {
                        taxAmountStr = taxAmountStr.replaceAll("[￥¥$,，\\s]", "").trim();
                        BigDecimal taxAmount = new BigDecimal(taxAmountStr);
                        bill.setTotalTaxAmount(taxAmount);
                    } catch (Exception e) {
                        log.warn("解析税额失败: {}, 明细ID: {}", taxAmountStr, detail.getId());
                    }
                }
            }

            // 设置小写金额
            if (rawData != null) {
                String amountInWords = rawData.get("价税合计(大写)");
                if (amountInWords == null) {
                    amountInWords = rawData.get("价税合计（大写）");
                }
                bill.setAmountInWords(amountInWords);
            }

            // 处理必填字段：摘要
            String summary = finalData.getString("summary");
            if (summary == null || summary.trim().isEmpty()) {
                summary = "发票";
            }
            bill.setSummary(summary);

            // 票据类型已在上面设置，这里不需要重复设置

            // 处理日期字段
            String billDateStr = finalData.getString("bill_date");
            if (billDateStr != null && !billDateStr.isEmpty()) {
                try {
                    java.text.SimpleDateFormat[] formats = {
                        new java.text.SimpleDateFormat("yyyy-MM-dd"),
                        new java.text.SimpleDateFormat("yyyy/MM/dd"),
                        new java.text.SimpleDateFormat("yyyy年MM月dd日")
                    };

                    for (java.text.SimpleDateFormat format : formats) {
                        try {
                            bill.setBillDate(format.parse(billDateStr));
                            break;
                        } catch (java.text.ParseException ignored) {
                            // 继续尝试下一种格式
                        }
                    }

                    if (bill.getBillDate() == null) {
                        log.warn("无法解析日期格式: {}, 使用当前日期", billDateStr);
                        bill.setBillDate(new Date());
                    }
                } catch (Exception e) {
                    log.warn("解析日期失败: {}, 使用当前日期", billDateStr, e);
                    bill.setBillDate(new Date());
                }
            } else {
                bill.setBillDate(new Date());
            }

            // 设置图片路径
            bill.setAttachmentPath(detail.getImageUrl());

            // 设置OCR识别信息（使用易读文本格式保存）
            String ocrRecognitionInfo = finalData.getString("ocrRecognitionInfo");
            if (ocrRecognitionInfo == null || ocrRecognitionInfo.trim().isEmpty()) {
                // 如果没有格式化的OCR信息，尝试从原始数据生成
                try {
                    JSONObject originalData = JSON.parseObject(detail.getRecognitionResult());
                    @SuppressWarnings("unchecked")
                    Map<String, String> rawOcrData = (Map<String, String>) originalData.get("rawOcrData");
                    if (rawOcrData != null && ocrService != null) {
                        ocrRecognitionInfo = ocrService.formatOcrDataToReadableText(rawOcrData, "INVOICE");
                    } else {
                        ocrRecognitionInfo = detail.getRecognitionResult();
                    }
                } catch (Exception e) {
                    log.warn("解析OCR原始数据失败，使用原始结果", e);
                    ocrRecognitionInfo = detail.getRecognitionResult();
                }
            }
            bill.setOcrRecognitionInfo(ocrRecognitionInfo);

            // 设置备注信息
            String userRemark = finalData.getString("remark");
            if (userRemark != null && !userRemark.trim().isEmpty()) {
                bill.setRemark(userRemark);
            }

            // 设置账套ID
            bill.setAccountSetsId(detail.getAccountSetsId());

            // 设置年月期间 - 使用账套当前期间而不是当前日期
            Integer[] currentPeriod = getCurrentAccountPeriod(detail.getAccountSetsId());
            bill.setBillYear(currentPeriod[0]);
            bill.setBillMonth(currentPeriod[1]);

            // 设置创建信息
            bill.setCreateTime(new Date());
            bill.setUpdateTime(new Date());

            // 设置默认状态
            bill.setStatus("未使用");

            // 使用BillService的save方法，它会自动处理编号生成等逻辑
            billService.save(detail.getAccountSetsId(), bill, null);

            log.info("发票保存成功，明细ID: {}, 发票编号: {}", detail.getId(), bill.getBillNo());

        } catch (Exception e) {
            log.error("保存发票失败，明细ID: {}", detail.getId(), e);
            throw e;
        }
    }

    /**
     * 使用模板或LLM智能映射银行回单字段
     * @param ocrData OCR识别的原始数据
     * @return 映射后的标准字段数据
     */
    private JSONObject smartMapBankReceiptFields(JSONObject ocrData) {
        return smartMapBankReceiptFields(ocrData, null, null);
    }

    /**
     * 使用模板或LLM智能映射银行回单字段
     * @param ocrData OCR识别的原始数据
     * @param accountSetsId 账套ID
     * @param createUser 创建用户ID
     * @return 映射后的标准字段数据
     */
    private JSONObject smartMapBankReceiptFields(JSONObject ocrData, Integer accountSetsId, Integer createUser) {
        // 添加空值检查
        if (ocrData == null) {
            log.warn("OCR数据为空，返回空的JSON对象");
            return new JSONObject();
        }

        try {
            // 使用SmartFieldMappingService进行智能字段映射
            if (smartFieldMappingService != null) {
                // 将JSONObject转换为Map<String, Object>
                Map<String, Object> ocrDataMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : ocrData.entrySet()) {
                    ocrDataMap.put(entry.getKey(), entry.getValue());
                }

                // 调用智能字段映射服务
                Map<String, Object> mappedData = smartFieldMappingService.mapBankReceiptFields(
                        ocrDataMap, accountSetsId, createUser);

                if (mappedData != null && !mappedData.isEmpty()) {
                    log.info("智能字段映射成功，映射字段数: {}", mappedData.size());
                    // 转换回JSONObject
                    JSONObject result = new JSONObject();
                    for (Map.Entry<String, Object> entry : mappedData.entrySet()) {
                        result.put(entry.getKey(), entry.getValue());
                    }
                    return result;
                }
            }

            // 如果智能映射服务不可用，回退到原有逻辑
            log.warn("智能字段映射服务不可用，使用原始数据");
            return ocrData;

        } catch (Exception e) {
            log.error("智能字段映射异常，使用原始数据", e);
            return ocrData;
        }
    }

    /**
     * 使用模板或LLM智能映射发票字段
     * @param ocrData OCR识别数据
     * @param accountSetsId 账套ID
     * @param createUser 创建用户ID
     * @return 映射后的数据
     */
    private JSONObject smartMapBillFields(JSONObject ocrData, Integer accountSetsId, Integer createUser) {
        if (ocrData == null || ocrData.isEmpty()) {
            log.warn("OCR数据为空，无法进行字段映射");
            return ocrData;
        }

        try {
            // 使用SmartFieldMappingService进行智能字段映射（与单个文件上传保持一致）
            if (smartFieldMappingService != null) {
                // 将JSONObject转换为Map<String, Object>
                Map<String, Object> ocrDataMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : ocrData.entrySet()) {
                    ocrDataMap.put(entry.getKey(), entry.getValue());
                }

                // 调用智能字段映射服务
                Map<String, Object> mappedData = smartFieldMappingService.mapInvoiceFields(
                        ocrDataMap, accountSetsId, createUser);

                if (mappedData != null && !mappedData.isEmpty()) {
                    log.info("智能发票字段映射成功，映射字段数: {}", mappedData.size());
                    // 转换回JSONObject
                    JSONObject result = new JSONObject();
                    for (Map.Entry<String, Object> entry : mappedData.entrySet()) {
                        result.put(entry.getKey(), entry.getValue());
                    }

                    // 修正发票类型字段：从OCR识别数据中提取真实的发票类型
                    try {
                        // 尝试从ocrRecognitionInfoJson中提取发票类型
                        String ocrRecognitionInfoJson = (String) result.get("ocrRecognitionInfoJson");
                        if (ocrRecognitionInfoJson != null && !ocrRecognitionInfoJson.trim().isEmpty()) {
                            JSONObject ocrJson = JSONObject.parseObject(ocrRecognitionInfoJson);
                            JSONObject recognitionData = ocrJson.getJSONObject("recognitionData");
                            if (recognitionData != null) {
                                String invoiceType = recognitionData.getString("发票类型");
                                if (invoiceType == null || invoiceType.trim().isEmpty()) {
                                    invoiceType = recognitionData.getString("发票名称");
                                }
                                if (invoiceType == null || invoiceType.trim().isEmpty()) {
                                    invoiceType = recognitionData.getString("票据类型");
                                }
                                if (invoiceType != null && !invoiceType.trim().isEmpty()) {
                                    result.put("type", invoiceType);
                                    log.info("修正字段映射中的发票类型: {} -> {}", mappedData.get("type"), invoiceType);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.warn("修正发票类型时解析OCR数据失败", e);
                    }

                    return result;
                } else {
                    log.warn("智能发票字段映射失败，使用原始数据");
                    return ocrData;
                }
            } else {
                log.warn("SmartFieldMappingService不可用，使用原始数据");
                return ocrData;
            }

        } catch (Exception e) {
            log.error("智能发票字段映射异常，使用原始数据", e);
            return ocrData;
        }
    }

    /**
     * 构建发票字段映射的LLM提示词
     */
    private String buildInvoiceFieldMappingPrompt(JSONObject ocrData) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请将以下增值税发票OCR识别结果映射到标准字段格式。\n\n");

        prompt.append("标准字段格式（JSON）：\n");
        prompt.append("- type: 票据类型（默认：发票）\n");
        prompt.append("- amount: 金额（数字，必填字段，请使用'价税合计(小写)'字段的值）\n");
        prompt.append("- issuer: 开票方/销售方名称\n");
        prompt.append("- recipient: 收票方/购买方名称\n");
        prompt.append("- invoice_number: 发票号码\n");
        prompt.append("- invoice_code: 发票代码\n");
        prompt.append("- bill_date: 开票日期（格式：yyyy-MM-dd）\n");
        prompt.append("- summary: 摘要（默认：发票）\n");
        prompt.append("- buyer_tax_id: 购买方纳税人识别号\n");
        prompt.append("- seller_tax_id: 销售方纳税人识别号\n");
        prompt.append("- amount_without_tax: 不含税金额（使用'合计金额'字段）\n");
        prompt.append("- total_tax_amount: 税额（使用'合计税额'字段）\n");
        prompt.append("- total_amount: 价税合计（使用'价税合计(小写)'字段）\n");
        prompt.append("- remark: 备注\n\n");

        // 获取原始OCR数据用于显示
        @SuppressWarnings("unchecked")
        Map<String, String> rawData = (Map<String, String>) ocrData.get("rawOcrData");
        if (rawData != null && !rawData.isEmpty()) {
            prompt.append("OCR识别的原始数据：\n");
            for (Map.Entry<String, String> entry : rawData.entrySet()) {
                prompt.append("- ").append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
            }
        }

        prompt.append("\n请根据OCR识别的原始数据，映射到标准字段格式，并返回JSON格式的结果。");
        prompt.append("注意：\n");
        prompt.append("1. amount字段必须有值，请使用'价税合计(小写)'字段，去除货币符号（如￥），只保留数字\n");
        prompt.append("2. 日期格式统一为yyyy-MM-dd\n");
        prompt.append("3. 如果某个字段在OCR数据中找不到对应值，可以设置为null，但amount字段必须有值\n");
        prompt.append("4. 金额相关字段都要去除货币符号，转换为纯数字格式\n");
        prompt.append("4. 请确保返回的是有效的JSON格式\n");

        return prompt.toString();
    }

    /**
     * 构建字段映射的LLM提示词
     */
    private String buildFieldMappingPrompt(JSONObject ocrData) {
        // 添加空值检查
        if (ocrData == null) {
            log.warn("OCR数据为空，无法构建字段映射提示词");
            return "OCR数据为空，无法进行字段映射";
        }

        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一个银行回单字段映射专家。请将OCR识别的银行回单字段映射到标准的数据库字段。\n\n");

        prompt.append("标准字段定义：\n");
        prompt.append("- receipt_title: 回单标题/类型（如：支付转账、代发工资、汇总收费扣款等）\n");
        prompt.append("- amount: 金额（数字，如：1000.50）\n");
        prompt.append("- payer_name: 付款人姓名\n");
        prompt.append("- payer_account: 付款人账号\n");
        prompt.append("- payer_bank: 付款人开户行\n");
        prompt.append("- payee_name: 收款人姓名\n");
        prompt.append("- payee_account: 收款人账号\n");
        prompt.append("- payee_bank: 收款人开户行\n");
        prompt.append("- serial_number: 流水号/交易序号\n");
        prompt.append("- transaction_institution: 交易机构（如：交通银行、工商银行等）\n");
        prompt.append("- amount_in_words: 金额大写\n");
        prompt.append("- summary: 摘要/用途（必填字段）\n");
        prompt.append("- receipts_date: 回单日期（格式：yyyy-MM-dd）\n");
        prompt.append("- transfer_date: 转账日期（格式：yyyy-MM-dd，必填字段）\n");
        prompt.append("- quantity: 张数（默认为1）\n");
        prompt.append("- type: 类型（收入/支出，必填字段，根据单据内容智能判断）\n\n");

        prompt.append("OCR识别的原始数据：\n");
        try {
            prompt.append(ocrData.toJSONString()).append("\n\n");
        } catch (Exception e) {
            log.warn("OCR数据序列化失败，使用toString方法", e);
            prompt.append(ocrData.toString()).append("\n\n");
        }

        prompt.append("请分析上述OCR数据，将其映射到标准字段。注意：\n");
        prompt.append("1. 不同银行的字段名称可能不同，请根据语义进行映射\n");
        prompt.append("2. 金额字段请转换为数字格式，去除逗号等符号\n");
        prompt.append("3. 日期字段请转换为yyyy-MM-dd格式\n");
        prompt.append("4. transfer_date（转账日期）是必填字段，如果OCR数据中没有明确的转账日期，可以使用交易日期、业务日期等相关日期\n");
        prompt.append("5. summary（摘要）是必填字段，如果OCR数据中没有明确的摘要，可以根据业务类型生成合适的摘要\n");
        prompt.append("6. quantity（张数）默认为1\n");
        prompt.append("7. type（类型）是必填字段，请根据单据内容智能判断：\n");
        prompt.append("   - 如果是转入、收款、存款、贷方发生额等，判断为\"收入\"\n");
        prompt.append("   - 如果是转出、付款、支付、借方发生额等，判断为\"支出\"\n");
        prompt.append("   - 根据回单标题、业务类型、摘要等综合判断收入支出性质\n");
        prompt.append("8. 对于\"对方户名\"字段，请根据交易类型智能判断：\n");
        prompt.append("   - 如果是出账/支出类型，对方户名通常是收款人(payee_name)\n");
        prompt.append("   - 如果是入账/收入类型，对方户名通常是付款人(payer_name)\n");
        prompt.append("   - 请结合回单类型、业务类型等信息综合判断\n");
        prompt.append("9. 如果某个非必填字段在OCR数据中找不到对应值，请设为null\n");
        prompt.append("10. 请只返回JSON格式的映射结果，不要包含其他文字说明\n\n");

        prompt.append("返回格式示例：\n");
        prompt.append("{\n");
        prompt.append("  \"receipt_title\": \"支付转账\",\n");
        prompt.append("  \"amount\": 1000.50,\n");
        prompt.append("  \"payer_name\": \"张三\",\n");
        prompt.append("  \"payer_account\": \"****************\",\n");
        prompt.append("  \"payee_name\": \"李四\",\n");
        prompt.append("  \"payee_account\": \"****************\",\n");
        prompt.append("  \"summary\": \"转账备注\",\n");
        prompt.append("  \"receipts_date\": \"2025-06-29\",\n");
        prompt.append("  \"transfer_date\": \"2025-06-29\",\n");
        prompt.append("  \"quantity\": 1,\n");
        prompt.append("  \"type\": \"支出\"\n");
        prompt.append("}\n");

        return prompt.toString();
    }

    /**
     * 解析AI映射响应
     */
    private JSONObject parseAiMappingResponse(String aiResponse) {
        try {
            // 清理响应文本，提取JSON部分
            String jsonStr = extractJsonFromResponse(aiResponse);
            if (jsonStr != null) {
                return JSON.parseObject(jsonStr);
            }
            return null;
        } catch (Exception e) {
            log.error("解析AI映射响应失败: {}", aiResponse, e);
            return null;
        }
    }

    /**
     * 从AI响应中提取JSON字符串
     */
    private String extractJsonFromResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return null;
        }

        // 查找JSON开始和结束位置
        int startIndex = response.indexOf('{');
        int endIndex = response.lastIndexOf('}');

        if (startIndex >= 0 && endIndex > startIndex) {
            return response.substring(startIndex, endIndex + 1);
        }

        return null;
    }

    /**
     * 检查银行回单是否重复
     * 基于流水号、金额、日期等关键字段进行重复检测
     * @return 如果重复返回重复信息，否则返回null
     */
    private String checkBankReceiptDuplicate(JSONObject finalData, Integer accountSetsId) {
        try {
            String serialNumber = finalData.getString("serial_number");
            Double amount = null;
            try {
                amount = finalData.getDouble("amount");
            } catch (Exception e) {
                // 如果getDouble失败，尝试作为字符串处理
                String amountStr = finalData.getString("amount");
                if (amountStr != null) {
                    try {
                        // 清理金额格式，移除引号、CNY前缀和逗号
                        amountStr = amountStr.replace("\"", "").replace("CNY", "").replace("¥", "").replace(",", "").trim();
                        amount = Double.parseDouble(amountStr);
                    } catch (NumberFormatException ex) {
                        log.warn("重复检测时无法解析金额字段: {}", amountStr);
                    }
                }
            }
            String transferDate = finalData.getString("transfer_date");
            String payerAccount = finalData.getString("payer_account");

            // 如果关键字段缺失，不进行重复检测
            if (serialNumber == null && amount == null) {
                return null;
            }

            // 构建查询条件
            LambdaQueryWrapper<BankReceipts> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BankReceipts::getAccountSetsId, accountSetsId);

            // 优先使用流水号进行精确匹配
            if (serialNumber != null && !serialNumber.trim().isEmpty()) {
                wrapper.eq(BankReceipts::getSerialNumber, serialNumber.trim());
                long count = bankReceiptsService.count(wrapper);
                log.info("重复检测 - 流水号: {}, 账套ID: {}, 查询结果: {}", serialNumber.trim(), accountSetsId, count);
                if (count > 0) {
                    String duplicateInfo = String.format("流水号 '%s' 已存在", serialNumber);
                    log.info("发现重复银行回单（流水号匹配）: {}", serialNumber);
                    return duplicateInfo;
                }
            }

            // 如果流水号没有重复，使用组合条件检测
            if (amount != null && transferDate != null && payerAccount != null) {
                LambdaQueryWrapper<BankReceipts> wrapper2 = new LambdaQueryWrapper<>();
                wrapper2.eq(BankReceipts::getAccountSetsId, accountSetsId)
                        .eq(BankReceipts::getAmount, amount)
                        .eq(BankReceipts::getTransferDate, transferDate)
                        .eq(BankReceipts::getPayerAccount, payerAccount.trim());

                long count = bankReceiptsService.count(wrapper2);
                if (count > 0) {
                    String duplicateInfo = String.format("相同金额 %.2f 元、日期 %s、付款账号 %s 的回单已存在",
                            amount, transferDate, payerAccount);
                    log.info("发现重复银行回单（组合条件匹配）: 金额={}, 日期={}, 付款账号={}",
                            amount, transferDate, payerAccount);
                    return duplicateInfo;
                }
            }

            return null;

        } catch (Exception e) {
            log.error("检查银行回单重复时发生异常", e);
            // 异常时不阻止保存，但记录日志
            return null;
        }
    }

    /**
     * 检查银行回单是否重复（保持向后兼容）
     * @deprecated 使用 checkBankReceiptDuplicate 替代
     */
    @Deprecated
    private boolean isDuplicateBankReceipt(JSONObject finalData, Integer accountSetsId) {
        return checkBankReceiptDuplicate(finalData, accountSetsId) != null;
    }

    /**
     * 检查票据是否重复
     * 基于发票号码、金额、日期等关键字段进行重复检测
     * @return 如果重复返回重复信息，否则返回null
     */
    private String checkBillDuplicate(JSONObject finalData, Integer accountSetsId) {
        try {
            String invoiceNumber = finalData.getString("invoice_number");
            Double amount = null;
            try {
                amount = finalData.getDouble("amount");
            } catch (Exception e) {
                // 如果getDouble失败，尝试作为字符串处理
                String amountStr = finalData.getString("amount");
                if (amountStr != null) {
                    try {
                        // 清理金额格式，移除引号、CNY前缀和逗号
                        amountStr = amountStr.replace("\"", "").replace("CNY", "").replace("¥", "").replace(",", "").trim();
                        amount = Double.parseDouble(amountStr);
                    } catch (NumberFormatException ex) {
                        log.warn("发票重复检测时无法解析金额字段: {}", amountStr);
                    }
                }
            }
            String billDate = finalData.getString("bill_date");
            String issuer = finalData.getString("issuer");

            // 如果关键字段缺失，不进行重复检测
            if (invoiceNumber == null && amount == null) {
                return null;
            }

            // 构建查询条件
            LambdaQueryWrapper<Bill> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Bill::getAccountSetsId, accountSetsId);

            // 优先使用发票号码进行精确匹配
            if (invoiceNumber != null && !invoiceNumber.trim().isEmpty()) {
                wrapper.eq(Bill::getInvoiceNumber, invoiceNumber.trim());
                long count = billService.count(wrapper);
                if (count > 0) {
                    String duplicateInfo = String.format("发票号码 '%s' 已存在", invoiceNumber);
                    log.info("发现重复票据（发票号码匹配）: {}", invoiceNumber);
                    return duplicateInfo;
                }
            }

            // 如果发票号码没有重复，使用组合条件检测
            if (amount != null && billDate != null && issuer != null) {
                LambdaQueryWrapper<Bill> wrapper2 = new LambdaQueryWrapper<>();
                wrapper2.eq(Bill::getAccountSetsId, accountSetsId)
                        .eq(Bill::getAmount, amount)
                        .eq(Bill::getBillDate, billDate)
                        .eq(Bill::getIssuer, issuer.trim());

                long count = billService.count(wrapper2);
                if (count > 0) {
                    String duplicateInfo = String.format("相同金额 %.2f 元、日期 %s、开票方 %s 的发票已存在",
                            amount, billDate, issuer);
                    log.info("发现重复票据（组合条件匹配）: 金额={}, 日期={}, 开票方={}",
                            amount, billDate, issuer);
                    return duplicateInfo;
                }
            }

            return null;

        } catch (Exception e) {
            log.error("检查票据重复时发生异常", e);
            // 异常时不阻止保存，但记录日志
            return null;
        }
    }

    /**
     * 检查票据是否重复（保持向后兼容）
     * @deprecated 使用 checkBillDuplicate 替代
     */
    @Deprecated
    private boolean isDuplicateBill(JSONObject finalData, Integer accountSetsId) {
        return checkBillDuplicate(finalData, accountSetsId) != null;
    }

    /**
     * 获取账套当前期间
     * @param accountSetsId 账套ID
     * @return [年份, 月份]
     */
    private Integer[] getCurrentAccountPeriod(Integer accountSetsId) {
        try {
            // 从数据库获取账套信息
            AccountSets accountSets = accountSetsService.getById(accountSetsId);
            if (accountSets != null && accountSets.getCurrentAccountDate() != null) {
                java.time.LocalDate currentAccountDate = accountSets.getCurrentAccountDate().toInstant()
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDate();
                log.info("使用账套当前期间: {}-{}", currentAccountDate.getYear(), currentAccountDate.getMonthValue());
                return new Integer[]{currentAccountDate.getYear(), currentAccountDate.getMonthValue()};
            } else {
                // 如果没有当前期间，使用当前日期
                java.time.LocalDate now = java.time.LocalDate.now();
                log.warn("账套当前期间为空，使用当前日期: {}-{}", now.getYear(), now.getMonthValue());
                return new Integer[]{now.getYear(), now.getMonthValue()};
            }
        } catch (Exception e) {
            log.error("获取账套当前期间失败，使用当前日期", e);
            java.time.LocalDate now = java.time.LocalDate.now();
            return new Integer[]{now.getYear(), now.getMonthValue()};
        }
    }
}
