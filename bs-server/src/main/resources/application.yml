server:
  port: 9080  # 将端口改为8081
  # 增加连接超时配置，防止大文件上传时连接断开
  tomcat:
    connection-timeout: 300000  # 5分钟连接超时
    max-http-post-size: 104857600  # 100MB POST请求大小限制
  # HTTP超时配置
  servlet:
    session:
      timeout: 30m  # 会话超时30分钟
spring:
  datasource:
    url: ******************************************************************************************************************************************************************
    password: root
    username: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 2
      minimum-idle: 2
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
  redis:
    # Redis数据库索引（默认为0）
    database: 10
    # Redis服务器地址
    host: localhost
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    # password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0

logging:
  level:
    "cn.gson.financial.kernel.model.mapper": debug


mybatis-plus:
  type-aliases-package: cn.gson.financial.kernel.model.entity
  configuration:
    call-setters-on-nulls: true
    map-underscore-to-camel-case: true
  mapper-locations:
    - classpath:/mappers/*Mapper.xml

aliyun:
  accessKeyId: LTAI5t81dQXCYoVwMocQL9CB
  accessKeySecret: ******************************
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    bucketName: aiform-f
  sms:
    signature: AI模范
    template-code:
      verification: SMS_175580136
      register: SMS_183262497

sa-token:
  token-session-check-login: false
  is-share: false
  is-concurrent: true
  token-name: fsatoken


tencent:
  cloud:
    secret-id: AKIDviIhw2yjS1VwCShnhQxHYdaURMl5DA8x
    secret-key: lWGnAMdszudpqdGm6KRRMAtY7Xoz55zl
    region: ap-beijing

# AI配置
ai:
  # 是否启用AI功能
  enabled: true
  # API基础URL (DeepSeek API)
  base-url: https://api.deepseek.com/v1
  # API密钥 (请在此处填入您的API密钥)
  api-key: sk-test-key-placeholder
  # 默认模型
  default-model: deepseek-chat
  # 请求超时时间(秒)
  timeout: 60
  # 最大重试次数
  max-retries: 3
  # 温度参数
  temperature: 0.7
  # 最大token数
  max-tokens: 2000
