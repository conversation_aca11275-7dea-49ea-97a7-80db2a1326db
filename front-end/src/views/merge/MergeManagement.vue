<template>
  <app-content class="h-panel">
    <div class="h-panel-bar">
      <span class="h-panel-title">归并票证</span>
    </div>

    <div class="h-panel-body">
      <div class="margin-bottom">
        <Button @click="activeTab = 'document'" :class="{'h-btn-primary': activeTab === 'document'}">票据归并</Button>
        <Button @click="activeTab = 'receipt'" :class="{'h-btn-primary': activeTab === 'receipt'}">银证归并</Button>
        <Button @click="activeTab = 'group'" :class="{'h-btn-primary': activeTab === 'group'}">归并组管理</Button>
      </div>

      <!-- 票据归并 -->
      <div v-if="activeTab === 'document'" class="merge-panel">
        <h3>票据归并</h3>
        <div class="margin-bottom">
          <Button @click="documentSubTab = 'auto'" :class="{'h-btn-primary': documentSubTab === 'auto'}">自动归并</Button>
          <Button @click="documentSubTab = 'manual'" :class="{'h-btn-primary': documentSubTab === 'manual'}">手动归并</Button>
          <Button @click="documentSubTab = 'ai'" :class="{'h-btn-primary': documentSubTab === 'ai'}">🤖 AI智能归并</Button>
        </div>

        <!-- 自动归并 -->
        <div v-if="documentSubTab === 'auto'">
          <div class="toolbar margin-bottom">
            <Select v-model="selectedDocumentRule" :datas="documentRuleOptions" placeholder="选择归并规则" style="width: 200px; margin-right: 10px;"></Select>
            <Button color="primary" @click="previewDocumentMerge" :disabled="!selectedDocumentRule">
              预览归并
            </Button>
          </div>

          <div class="toolbar margin-bottom" v-if="selectedDocumentRule">
            <input
              v-model="documentGroupName"
              placeholder="请输入归并组名称"
              class="h-input"
              style="width: 300px; margin-right: 10px; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;"
            />
            <Button color="green" @click="executeDocumentMerge" :disabled="!selectedDocumentRule || !documentGroupName.trim()">
              执行归并
            </Button>
          </div>
        </div>

        <!-- 手动归并 -->
        <div v-if="documentSubTab === 'manual'">
          <div class="toolbar margin-bottom">
            <Button @click="loadUnmergedDocuments" icon="h-icon-refresh">刷新数据</Button>
            <input
              v-model="manualDocumentGroupName"
              placeholder="请输入归并组名称"
              class="h-input"
              style="width: 300px; margin: 0 10px; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;"
            />
            <Button color="green" @click="executeManualDocumentMerge" :disabled="!canExecuteDocumentMerge">
              执行手动归并 ({{ selectedDocuments.length }}项)
            </Button>
          </div>

          <table class="h-table h-table-border">
            <thead>
              <tr>
                <th style="width: 50px">
                  <Checkbox v-model="selectAllDocuments" @change="toggleSelectAllDocuments">全选</Checkbox>
                </th>
                <th style="width: 120px">票据编号</th>
                <th style="width: 100px">日期</th>
                <th style="width: 100px">金额</th>
                <th>摘要</th>
                <th style="width: 120px">开票方</th>
                <th style="width: 120px">收票方</th>
                <th style="width: 100px">类型</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="doc in unmergedDocuments" :key="doc.id">
                <td>
                  <Checkbox v-model="selectedDocuments" :value="doc.id"></Checkbox>
                </td>
                <td>{{ doc.billNo }}</td>
                <td>{{ doc.billDate }}</td>
                <td>¥{{ doc.amount }}</td>
                <td>{{ doc.summary }}</td>
                <td>{{ doc.issuer }}</td>
                <td>{{ doc.recipient }}</td>
                <td>{{ doc.type }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- AI智能归并 -->
        <div v-if="documentSubTab === 'ai'">
          <div class="ai-merge-section">
            <div class="ai-info-panel margin-bottom">
              <h4>🤖 AI智能归并</h4>
              <p>AI将自动分析票据数据，识别可以归并的票据组合，基于开票方、日期、金额、用途等因素进行智能匹配。</p>
            </div>

            <div class="toolbar margin-bottom">
              <Button @click="loadUnmergedDocumentsForAi" icon="h-icon-refresh">刷新数据</Button>
              <Button color="primary" @click="analyzeDocumentMergeWithAi"
                      :disabled="aiUnmergedDocuments.length === 0 || aiAnalyzing"
                      :loading="aiAnalyzing">
                {{ aiAnalyzing ? '🤖 AI分析中...' : '🔍 AI分析归并建议' }} ({{ aiUnmergedDocuments.length }}个票据)
              </Button>
              <Button color="green" @click="executeSelectedAiDocumentMerge"
                      :disabled="!aiDocumentSuggestions || selectedAiDocumentGroups.length === 0">
                ⚡ 执行选中归并 ({{ selectedAiDocumentGroups.length }}个组)
              </Button>
              <Button color="orange" @click="autoMergeAllDocuments" :disabled="aiUnmergedDocuments.length === 0">
                🚀 一键智能归并
              </Button>
            </div>

            <!-- AI分析结果 -->
            <div v-if="aiDocumentSuggestions" class="ai-suggestions margin-bottom">
              <h4>AI归并建议</h4>
              <div class="suggestion-summary margin-bottom">
                <span class="h-tag">建议组数: {{ aiDocumentSuggestions.groups.length }}</span>
                <span class="h-tag h-tag-blue">可归并票据: {{ getTotalSuggestedItems(aiDocumentSuggestions.groups) }}个</span>
                <span class="h-tag h-tag-green">{{ aiDocumentSuggestions.message }}</span>
                <span class="h-tag h-tag-orange">已选择: {{ selectedAiDocumentGroups.length }}个组</span>
              </div>

              <div class="suggestion-actions margin-bottom">
                <Button size="s" @click="selectAllAiDocumentGroups">全选</Button>
                <Button size="s" @click="clearAiDocumentGroupSelection">清空选择</Button>
              </div>

              <div v-for="(group, index) in aiDocumentSuggestions.groups" :key="index" class="suggestion-group margin-bottom">
                <div class="suggestion-header">
                  <Checkbox v-model="selectedAiDocumentGroups" :value="index" style="margin-right: 10px;"></Checkbox>
                  <h5 style="display: inline-block; margin: 0;">{{ group.groupName }} ({{ group.billIds.length }}个票据)</h5>
                </div>
                <p class="suggestion-reason">归并原因: {{ group.reason }}</p>
                <div class="suggested-items">
                  <span v-for="billId in group.billIds" :key="billId" class="h-tag h-tag-yellow">
                    票据ID: {{ billId }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 未归并票据列表 -->
            <div class="unmerged-list">
              <h4>未归并票据列表 ({{ aiUnmergedDocuments.length }}个)</h4>
              <table class="h-table h-table-border">
                <thead>
                  <tr>
                    <th style="width: 80px">ID</th>
                    <th style="width: 120px">票据编号</th>
                    <th style="width: 100px">日期</th>
                    <th style="width: 100px">金额</th>
                    <th>摘要</th>
                    <th style="width: 120px">开票方</th>
                    <th style="width: 100px">类型</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="doc in aiUnmergedDocuments" :key="doc.id">
                    <td>{{ doc.id }}</td>
                    <td>{{ doc.billNo }}</td>
                    <td>{{ doc.billDate }}</td>
                    <td>¥{{ doc.amount }}</td>
                    <td>{{ doc.summary }}</td>
                    <td>{{ doc.issuer }}</td>
                    <td>{{ doc.type }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 预览结果 -->
        <div v-if="documentPreview" class="preview-section margin-top">
          <h4>归并预览结果</h4>
          <div class="preview-summary margin-bottom">
            <span class="h-tag">总组数: {{ documentPreview.totalGroups }}</span>
            <span class="h-tag h-tag-blue">总项目数: {{ documentPreview.totalItems }}</span>
            <span class="h-tag h-tag-yellow">总金额: ¥{{ documentPreview.totalAmount }}</span>
          </div>

          <div v-for="(group, index) in documentPreview.groups" :key="index" class="group-item margin-bottom">
            <h5>{{ group.groupName }} ({{ group.itemCount }}项, ¥{{ group.totalAmount }})</h5>
            <table class="h-table h-table-border">
              <thead>
                <tr>
                  <th style="width: 120px">票据编号</th>
                  <th style="width: 100px">日期</th>
                  <th style="width: 100px">金额</th>
                  <th>摘要</th>
                  <th style="width: 120px">交易方</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in group.items" :key="item.itemNo">
                  <td>{{ item.itemNo }}</td>
                  <td>{{ item.itemDate }}</td>
                  <td>¥{{ item.amount }}</td>
                  <td>{{ item.summary }}</td>
                  <td>{{ item.counterparty }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 银证归并 -->
      <div v-if="activeTab === 'receipt'" class="merge-panel">
        <h3>银证归并</h3>
        <div class="margin-bottom">
          <Button @click="receiptSubTab = 'auto'" :class="{'h-btn-primary': receiptSubTab === 'auto'}">自动归并</Button>
          <Button @click="receiptSubTab = 'manual'" :class="{'h-btn-primary': receiptSubTab === 'manual'}">手动归并</Button>
          <Button @click="receiptSubTab = 'ai'" :class="{'h-btn-primary': receiptSubTab === 'ai'}">🤖 AI智能归并</Button>
        </div>

        <!-- 自动归并 -->
        <div v-if="receiptSubTab === 'auto'">
          <div class="toolbar margin-bottom">
            <Select v-model="selectedReceiptRule" :datas="receiptRuleOptions" placeholder="选择归并规则" style="width: 200px; margin-right: 10px;"></Select>
            <Button color="primary" @click="previewReceiptMerge" :disabled="!selectedReceiptRule">
              预览归并
            </Button>
          </div>

          <div class="toolbar margin-bottom" v-if="selectedReceiptRule">
            <input
              v-model="receiptGroupName"
              placeholder="请输入归并组名称"
              class="h-input"
              style="width: 300px; margin-right: 10px; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;"
            />
            <Button color="green" @click="executeReceiptMerge" :disabled="!selectedReceiptRule || !receiptGroupName.trim()">
              执行归并
            </Button>
          </div>
        </div>

        <!-- 手动归并 -->
        <div v-if="receiptSubTab === 'manual'">
          <div class="toolbar margin-bottom">
            <Button @click="loadUnmergedReceipts" icon="h-icon-refresh">刷新数据</Button>
            <input
              v-model="manualReceiptGroupName"
              placeholder="请输入归并组名称"
              class="h-input"
              style="width: 300px; margin: 0 10px; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;"
            />
            <Button color="green" @click="executeManualReceiptMerge" :disabled="!canExecuteReceiptMerge">
              执行手动归并 ({{ selectedReceipts.length }}项)
            </Button>
          </div>

          <table class="h-table h-table-border">
            <thead>
              <tr>
                <th style="width: 50px">
                  <Checkbox v-model="selectAllReceipts" @change="toggleSelectAllReceipts">全选</Checkbox>
                </th>
                <th style="width: 120px">银证编号</th>
                <th style="width: 100px">日期</th>
                <th style="width: 100px">金额</th>
                <th>摘要</th>
                <th style="width: 120px">交易方</th>
                <th style="width: 100px">类型</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="receipt in unmergedReceipts" :key="receipt.id">
                <td>
                  <Checkbox v-model="selectedReceipts" :value="receipt.id"></Checkbox>
                </td>
                <td>{{ receipt.receiptsNo }}</td>
                <td>{{ receipt.receiptsDate }}</td>
                <td>¥{{ receipt.amount }}</td>
                <td>{{ receipt.summary }}</td>
                <td>{{ receipt.counterparty }}</td>
                <td>{{ receipt.type }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- AI智能归并 -->
        <div v-if="receiptSubTab === 'ai'">
          <div class="ai-merge-section">
            <div class="ai-info-panel margin-bottom">
              <h4>🤖 AI智能银证归并</h4>
              <p>AI将自动分析银证数据，识别可以归并的银证组合，基于交易对手、日期、金额、用途等因素进行智能匹配。</p>
            </div>

            <div class="toolbar margin-bottom">
              <Button @click="loadUnmergedReceiptsForAi" icon="h-icon-refresh">刷新数据</Button>
              <Button color="primary" @click="analyzeReceiptMergeWithAi"
                      :disabled="aiUnmergedReceipts.length === 0 || aiAnalyzingReceipts"
                      :loading="aiAnalyzingReceipts">
                {{ aiAnalyzingReceipts ? '🤖 AI分析中...' : '🔍 AI分析归并建议' }} ({{ aiUnmergedReceipts.length }}个银证)
              </Button>
              <Button color="green" @click="executeSelectedAiReceiptMerge"
                      :disabled="!aiReceiptSuggestions || selectedAiReceiptGroups.length === 0">
                ⚡ 执行选中归并 ({{ selectedAiReceiptGroups.length }}个组)
              </Button>
              <Button color="orange" @click="autoMergeAllReceipts" :disabled="aiUnmergedReceipts.length === 0">
                🚀 一键智能归并
              </Button>
            </div>

            <!-- AI分析结果 -->
            <div v-if="aiReceiptSuggestions" class="ai-suggestions margin-bottom">
              <h4>AI归并建议</h4>
              <div class="suggestion-summary margin-bottom">
                <span class="h-tag">建议组数: {{ aiReceiptSuggestions.groups.length }}</span>
                <span class="h-tag h-tag-blue">可归并银证: {{ getTotalSuggestedReceiptItems(aiReceiptSuggestions.groups) }}个</span>
                <span class="h-tag h-tag-green">{{ aiReceiptSuggestions.message }}</span>
                <span class="h-tag h-tag-orange">已选择: {{ selectedAiReceiptGroups.length }}个组</span>
              </div>

              <div class="suggestion-actions margin-bottom">
                <Button size="s" @click="selectAllAiReceiptGroups">全选</Button>
                <Button size="s" @click="clearAiReceiptGroupSelection">清空选择</Button>
              </div>

              <div v-for="(group, index) in aiReceiptSuggestions.groups" :key="index" class="suggestion-group margin-bottom">
                <div class="suggestion-header">
                  <Checkbox v-model="selectedAiReceiptGroups" :value="index" style="margin-right: 10px;"></Checkbox>
                  <h5 style="display: inline-block; margin: 0;">{{ group.groupName }} ({{ group.receiptIds.length }}个银证)</h5>
                </div>
                <p class="suggestion-reason">归并原因: {{ group.reason }}</p>
                <div class="suggested-items">
                  <span v-for="receiptId in group.receiptIds" :key="receiptId" class="h-tag h-tag-yellow">
                    银证ID: {{ receiptId }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 未归并银证列表 -->
            <div class="unmerged-list">
              <h4>未归并银证列表 ({{ aiUnmergedReceipts.length }}个)</h4>
              <table class="h-table h-table-border">
                <thead>
                  <tr>
                    <th style="width: 80px">ID</th>
                    <th style="width: 120px">银证编号</th>
                    <th style="width: 100px">日期</th>
                    <th style="width: 100px">金额</th>
                    <th>摘要</th>
                    <th style="width: 120px">交易方</th>
                    <th style="width: 100px">类型</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="receipt in aiUnmergedReceipts" :key="receipt.id">
                    <td>{{ receipt.id }}</td>
                    <td>{{ receipt.receiptsNo }}</td>
                    <td>{{ receipt.receiptsDate }}</td>
                    <td>¥{{ receipt.amount }}</td>
                    <td>{{ receipt.summary }}</td>
                    <td>{{ receipt.counterparty }}</td>
                    <td>{{ receipt.type }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 预览结果 -->
        <div v-if="receiptPreview" class="preview-section margin-top">
          <h4>归并预览结果</h4>
          <div class="preview-summary margin-bottom">
            <span class="h-tag">总组数: {{ receiptPreview.totalGroups }}</span>
            <span class="h-tag h-tag-blue">总项目数: {{ receiptPreview.totalItems }}</span>
            <span class="h-tag h-tag-yellow">总金额: ¥{{ receiptPreview.totalAmount }}</span>
          </div>

          <div v-for="(group, index) in receiptPreview.groups" :key="index" class="group-item margin-bottom">
            <h5>{{ group.groupName }} ({{ group.itemCount }}项, ¥{{ group.totalAmount }})</h5>
            <table class="h-table h-table-border">
              <thead>
                <tr>
                  <th style="width: 120px">银证编号</th>
                  <th style="width: 100px">日期</th>
                  <th style="width: 100px">金额</th>
                  <th>摘要</th>
                  <th style="width: 120px">交易方</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in group.items" :key="item.itemNo">
                  <td>{{ item.itemNo }}</td>
                  <td>{{ item.itemDate }}</td>
                  <td>¥{{ item.amount }}</td>
                  <td>{{ item.summary }}</td>
                  <td>{{ item.counterparty }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 归并组管理 -->
      <div v-if="activeTab === 'group'" class="group-panel">
        <h3>归并组管理</h3>
        <div class="margin-bottom">
          <Button @click="groupSubTab = 'documentGroup'" :class="{'h-btn-primary': groupSubTab === 'documentGroup'}">票据归并组</Button>
          <Button @click="groupSubTab = 'receiptGroup'" :class="{'h-btn-primary': groupSubTab === 'receiptGroup'}">银证归并组</Button>
        </div>

        <!-- 票据归并组 -->
        <div v-if="groupSubTab === 'documentGroup'">
          <div class="toolbar margin-bottom">
            <Button @click="loadDocumentGroups" icon="h-icon-refresh">刷新</Button>
          </div>
          <table class="h-table h-table-border">
            <thead>
              <tr>
                <th style="width: 200px">组名称</th>
                <th style="width: 100px">项目数量</th>
                <th style="width: 120px">总金额</th>
                <th style="width: 100px">状态</th>
                <th style="width: 150px">创建时间</th>
                <th style="width: 200px">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="group in documentGroups" :key="group.groupId">
                <td>{{ group.groupName }}</td>
                <td>{{ group.itemCount }}</td>
                <td>¥{{ group.totalAmount }}</td>
                <td>
                  <span :class="group.status === 'ACTIVE' ? 'h-tag h-tag-green' : 'h-tag'">
                    {{ group.status === 'ACTIVE' ? '活跃' : '已解散' }}
                  </span>
                </td>
                <td>{{ group.createdAt }}</td>
                <td>
                  <Button size="s" @click="viewDocumentGroupDetail(group)">详情</Button>
                  <Button size="s" color="red" @click="dissolveDocumentGroup(group.groupId)"
                          :disabled="group.status !== 'ACTIVE'">
                    解散
                  </Button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 银证归并组 -->
        <div v-if="groupSubTab === 'receiptGroup'">
          <div class="toolbar margin-bottom">
            <Button @click="loadReceiptGroups" icon="h-icon-refresh">刷新</Button>
          </div>
          <table class="h-table h-table-border">
            <thead>
              <tr>
                <th style="width: 200px">组名称</th>
                <th style="width: 100px">项目数量</th>
                <th style="width: 120px">总金额</th>
                <th style="width: 100px">状态</th>
                <th style="width: 150px">创建时间</th>
                <th style="width: 200px">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="group in receiptGroups" :key="group.groupId">
                <td>{{ group.groupName }}</td>
                <td>{{ group.itemCount }}</td>
                <td>¥{{ group.totalAmount }}</td>
                <td>
                  <span :class="group.status === 'ACTIVE' ? 'h-tag h-tag-green' : 'h-tag'">
                    {{ group.status === 'ACTIVE' ? '活跃' : '已解散' }}
                  </span>
                </td>
                <td>{{ group.createdAt }}</td>
                <td>
                  <Button size="s" @click="viewReceiptGroupDetail(group)">详情</Button>
                  <Button size="s" color="red" @click="dissolveReceiptGroup(group.groupId)"
                          :disabled="group.status !== 'ACTIVE'">
                    解散
                  </Button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>


    </div>
  </app-content>

</template>

<style scoped>
.suggestion-group {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
}

.suggestion-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.suggestion-reason {
  color: #666;
  font-size: 14px;
  margin: 8px 0;
}

.suggested-items {
  margin-top: 8px;
}

.suggested-items .h-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.suggestion-actions {
  text-align: right;
}

.suggestion-actions .h-btn {
  margin-left: 8px;
}

.ai-suggestions {
  border: 1px solid #d4edda;
  border-radius: 4px;
  padding: 16px;
  background-color: #f8fff9;
}

.suggestion-summary .h-tag {
  margin-right: 8px;
}

.toolbar .h-btn {
  margin-right: 8px;
}
</style>

<script>
export default {
  name: 'MergeManagement',
  data() {
    return {
      activeTab: 'document',

      // 票据归并
      documentSubTab: 'auto',
      selectedDocumentRule: '',
      documentRules: [],
      documentPreview: null,
      documentGroupName: '',

      // 手动票据归并
      unmergedDocuments: [],
      selectedDocuments: [],
      selectAllDocuments: false,
      manualDocumentGroupName: '',

      // 银证归并
      receiptSubTab: 'auto',
      selectedReceiptRule: '',
      receiptRules: [],
      receiptPreview: null,
      receiptGroupName: '',

      // 手动银证归并
      unmergedReceipts: [],
      selectedReceipts: [],
      selectAllReceipts: false,
      manualReceiptGroupName: '',

      // 归并组管理
      groupSubTab: 'documentGroup',
      documentGroups: [],
      receiptGroups: [],

      // AI智能归并
      aiUnmergedDocuments: [],
      aiDocumentSuggestions: null,
      aiUnmergedReceipts: [],
      aiReceiptSuggestions: null,

      // AI分析状态
      aiAnalyzing: false,
      aiAnalyzingReceipts: false,

      // AI建议选择
      selectedAiDocumentGroups: [],
      selectedAiReceiptGroups: []
    }
  },

  computed: {
    documentRuleOptions() {
      return this.documentRules.map(rule => ({
        key: rule.ruleId,
        title: rule.ruleName
      }))
    },
    receiptRuleOptions() {
      return this.receiptRules.map(rule => ({
        key: rule.ruleId,
        title: rule.ruleName
      }))
    },
    canExecuteDocumentMerge() {
      const hasEnoughItems = this.selectedDocuments.length >= 2
      const hasGroupName = this.manualDocumentGroupName && this.manualDocumentGroupName.trim().length > 0
      console.log('canExecuteDocumentMerge:', {
        selectedCount: this.selectedDocuments.length,
        hasEnoughItems,
        groupName: this.manualDocumentGroupName,
        hasGroupName,
        result: hasEnoughItems && hasGroupName
      })
      return hasEnoughItems && hasGroupName
    },
    canExecuteReceiptMerge() {
      const hasEnoughItems = this.selectedReceipts.length >= 2
      const hasGroupName = this.manualReceiptGroupName && this.manualReceiptGroupName.trim().length > 0
      console.log('canExecuteReceiptMerge:', {
        selectedCount: this.selectedReceipts.length,
        hasEnoughItems,
        groupName: this.manualReceiptGroupName,
        hasGroupName,
        result: hasEnoughItems && hasGroupName
      })
      return hasEnoughItems && hasGroupName
    }
  },

  watch: {
    selectedDocumentRule(newRuleId) {
      if (newRuleId) {
        const rule = this.documentRules.find(r => r.ruleId === newRuleId)
        if (rule) {
          const now = new Date()
          const dateStr = now.getFullYear() + '年' + (now.getMonth() + 1) + '月' + now.getDate() + '日'
          this.documentGroupName = `${rule.ruleName}_${dateStr}`
        }
      } else {
        this.documentGroupName = ''
      }
    },
    selectedReceiptRule(newRuleId) {
      if (newRuleId) {
        const rule = this.receiptRules.find(r => r.ruleId === newRuleId)
        if (rule) {
          const now = new Date()
          const dateStr = now.getFullYear() + '年' + (now.getMonth() + 1) + '月' + now.getDate() + '日'
          this.receiptGroupName = `${rule.ruleName}_${dateStr}`
        }
      } else {
        this.receiptGroupName = ''
      }
    },
    selectedDocuments: {
      handler(newVal) {
        // 只在非全选操作时更新全选状态
        if (!this._isTogglingSelectAll) {
          // 如果所有项目都被选中，则全选为true；否则为false
          this.selectAllDocuments = newVal.length === this.unmergedDocuments.length && this.unmergedDocuments.length > 0
        }
      },
      deep: true
    },
    selectedReceipts: {
      handler(newVal) {
        // 只在非全选操作时更新全选状态
        if (!this._isTogglingSelectAll) {
          // 如果所有项目都被选中，则全选为true；否则为false
          this.selectAllReceipts = newVal.length === this.unmergedReceipts.length && this.unmergedReceipts.length > 0
        }
      },
      deep: true
    },
    manualDocumentGroupName(newVal) {
      console.log('manualDocumentGroupName changed:', newVal)
    },
    manualReceiptGroupName(newVal) {
      console.log('manualReceiptGroupName changed:', newVal)
    }
  },

  mounted() {
    this.loadMergeRules()
    this.loadDocumentGroups()
    this.loadReceiptGroups()
    this.loadUnmergedDocuments()
    this.loadUnmergedReceipts()
    // 加载AI相关数据
    this.loadUnmergedDataForAi()
  },

  methods: {
    // AI相关方法
    getTotalSuggestedItems(groups) {
      return groups.reduce((total, group) => total + (group.billIds ? group.billIds.length : 0), 0)
    },

    getTotalSuggestedReceiptItems(groups) {
      return groups.reduce((total, group) => total + (group.receiptIds ? group.receiptIds.length : 0), 0)
    },

    // AI数据加载方法
    async loadUnmergedDataForAi() {
      try {
        // 加载未归并的票据
        const billsResponse = await fetch('/api/ai-merge/documents/unmerged?limit=50', {
          credentials: 'include'
        })
        const billsResult = await billsResponse.json()
        if (billsResult.success) {
          this.aiUnmergedDocuments = billsResult.data || []
        }

        // 加载未归并的银证
        const receiptsResponse = await fetch('/api/ai-merge/receipts/unmerged?limit=50', {
          credentials: 'include'
        })
        const receiptsResult = await receiptsResponse.json()
        if (receiptsResult.success) {
          this.aiUnmergedReceipts = receiptsResult.data || []
        }

        console.log(`AI数据加载完成: ${this.aiUnmergedDocuments.length}个未归并票据, ${this.aiUnmergedReceipts.length}个未归并银证`)
      } catch (error) {
        console.error('加载AI数据失败:', error)
      }
    },

    // 加载归并规则
    async loadMergeRules() {
      try {
        // 从后端API获取归并规则
        const response = await fetch('/api/merge-rules', {
          credentials: 'include'
        })
        const result = await response.json()
        console.log('归并规则API响应:', result)
        if (result.success) {
          const rules = result.data || []
          this.documentRules = rules.filter(rule => rule.applicableEntity === 'DOCUMENT' || rule.applicableEntity === 'BOTH')
          this.receiptRules = rules.filter(rule => rule.applicableEntity === 'RECEIPT' || rule.applicableEntity === 'BOTH')
          console.log('加载归并规则成功:', rules.length, '票据规则:', this.documentRules.length, '银证规则:', this.receiptRules.length)
        } else {
          console.log('归并规则API返回失败:', result.msg)
          this.$Message.error('加载归并规则失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('加载归并规则失败: ' + error.message)
        console.error('加载归并规则失败:', error)
      }
    },

    // 加载票据归并组
    async loadDocumentGroups() {
      try {
        const response = await fetch('/api/merge-groups/documents', {
          credentials: 'include'
        })
        const result = await response.json()
        console.log('票据归并组API响应:', result)
        if (result.success) {
          this.documentGroups = result.data || []
          console.log('加载票据归并组成功:', this.documentGroups.length)
        } else {
          console.log('票据归并组API返回失败:', result.msg)
          this.$Message.error('加载票据归并组失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('加载票据归并组失败: ' + error.message)
        console.error('加载票据归并组失败:', error)
      }
    },

    // 加载银证归并组
    async loadReceiptGroups() {
      try {
        const response = await fetch('/api/merge-groups/receipts', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.receiptGroups = result.data || []
          console.log('加载银证归并组成功:', this.receiptGroups.length)
        }
      } catch (error) {
        this.$Message.error('加载银证归并组失败: ' + error.message)
        console.error('加载银证归并组失败:', error)
      }
    },



    // 加载未归并的票据
    async loadUnmergedDocuments() {
      try {
        const response = await fetch('/api/merge-engine/documents/unmerged', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.unmergedDocuments = result.data || []
          this.selectedDocuments = []
          this.selectAllDocuments = false
          console.log('加载未归并票据成功:', this.unmergedDocuments.length)
        } else {
          this.$Message.error('加载未归并票据失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('加载未归并票据失败: ' + error.message)
        console.error('加载未归并票据失败:', error)
      }
    },

    // 加载未归并的银证
    async loadUnmergedReceipts() {
      try {
        const response = await fetch('/api/merge-engine/receipts/unmerged', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.unmergedReceipts = result.data || []
          this.selectedReceipts = []
          this.selectAllReceipts = false
          console.log('加载未归并银证成功:', this.unmergedReceipts.length)
        } else {
          this.$Message.error('加载未归并银证失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('加载未归并银证失败: ' + error.message)
        console.error('加载未归并银证失败:', error)
      }
    },

    // 预览票据归并
    async previewDocumentMerge() {
      if (!this.selectedDocumentRule) {
        this.$Message.error('请先选择归并规则')
        return
      }
      try {
        const response = await fetch(`/api/merge-engine/documents/preview?ruleId=${this.selectedDocumentRule}`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        const result = await response.json()
        if (result.success) {
          this.documentPreview = result.data
          this.$Message.success('预览成功')
        } else {
          this.$Message.error('预览失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('预览票据归并失败: ' + error.message)
      }
    },

    // 执行票据归并
    async executeDocumentMerge() {
      if (!this.selectedDocumentRule) {
        this.$Message.error('请先选择归并规则')
        return
      }
      if (!this.documentGroupName.trim()) {
        this.$Message.error('请输入归并组名称')
        return
      }
      try {
        const response = await fetch('/api/merge-engine/documents/execute', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ruleId: this.selectedDocumentRule,
            groupName: this.documentGroupName.trim(),
            async: false
          })
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success('票据归并执行成功')
          this.documentPreview = null
          this.documentGroupName = ''
          this.selectedDocumentRule = ''
          this.loadDocumentGroups()
        } else {
          this.$Message.error('执行失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('执行票据归并失败: ' + error.message)
      }
    },

    // 预览银证归并
    async previewReceiptMerge() {
      if (!this.selectedReceiptRule) {
        this.$Message.error('请先选择归并规则')
        return
      }
      try {
        const response = await fetch(`/api/merge-engine/receipts/preview?ruleId=${this.selectedReceiptRule}`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        const result = await response.json()
        if (result.success) {
          this.receiptPreview = result.data
          this.$Message.success('预览成功')
        } else {
          this.$Message.error('预览失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('预览银证归并失败: ' + error.message)
      }
    },

    // 执行银证归并
    async executeReceiptMerge() {
      if (!this.selectedReceiptRule) {
        this.$Message.error('请先选择归并规则')
        return
      }
      if (!this.receiptGroupName.trim()) {
        this.$Message.error('请输入归并组名称')
        return
      }
      try {
        const response = await fetch('/api/merge-engine/receipts/execute', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ruleId: this.selectedReceiptRule,
            groupName: this.receiptGroupName.trim(),
            async: false
          })
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success('银证归并执行成功')
          this.receiptPreview = null
          this.receiptGroupName = ''
          this.selectedReceiptRule = ''
          this.loadReceiptGroups()
        } else {
          this.$Message.error('执行失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('执行银证归并失败: ' + error.message)
      }
    },

    // 执行手动票据归并
    async executeManualDocumentMerge() {
      if (this.selectedDocuments.length < 2) {
        this.$Message.error('请至少选择2个票据进行归并')
        return
      }
      if (!this.manualDocumentGroupName.trim()) {
        this.$Message.error('请输入归并组名称')
        return
      }
      try {
        const response = await fetch('/api/merge-engine/documents/manual-merge', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            documentIds: this.selectedDocuments,
            groupName: this.manualDocumentGroupName.trim()
          })
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success('手动票据归并执行成功')
          this.selectedDocuments = []
          this.selectAllDocuments = false
          this.manualDocumentGroupName = ''
          this.loadUnmergedDocuments()
          this.loadDocumentGroups()
        } else {
          this.$Message.error('执行失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('执行手动票据归并失败: ' + error.message)
      }
    },

    // 执行手动银证归并
    async executeManualReceiptMerge() {
      if (this.selectedReceipts.length < 2) {
        this.$Message.error('请至少选择2个银证进行归并')
        return
      }
      if (!this.manualReceiptGroupName.trim()) {
        this.$Message.error('请输入归并组名称')
        return
      }
      try {
        const response = await fetch('/api/merge-engine/receipts/manual-merge', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            receiptIds: this.selectedReceipts,
            groupName: this.manualReceiptGroupName.trim()
          })
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success('手动银证归并执行成功')
          this.selectedReceipts = []
          this.selectAllReceipts = false
          this.manualReceiptGroupName = ''
          this.loadUnmergedReceipts()
          this.loadReceiptGroups()
        } else {
          this.$Message.error('执行失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('执行手动银证归并失败: ' + error.message)
      }
    },

    // 切换全选票据
    toggleSelectAllDocuments() {
      console.log('toggleSelectAllDocuments called, selectAllDocuments:', this.selectAllDocuments)
      this._isTogglingSelectAll = true

      if (this.selectAllDocuments) {
        // 全选被选中 -> 选中所有项目
        this.selectedDocuments = this.unmergedDocuments.map(doc => doc.id)
        console.log('全选：选中所有项目', this.selectedDocuments.length)
      } else {
        // 全选被取消 -> 清空所有选择
        this.selectedDocuments = []
        console.log('取消全选：清空所有选择')
      }

      this.$nextTick(() => {
        this._isTogglingSelectAll = false
      })
    },

    // 切换全选银证
    toggleSelectAllReceipts() {
      console.log('toggleSelectAllReceipts called, selectAllReceipts:', this.selectAllReceipts)
      this._isTogglingSelectAll = true

      if (this.selectAllReceipts) {
        // 全选被选中 -> 选中所有项目
        this.selectedReceipts = this.unmergedReceipts.map(receipt => receipt.id)
        console.log('全选：选中所有项目', this.selectedReceipts.length)
      } else {
        // 全选被取消 -> 清空所有选择
        this.selectedReceipts = []
        console.log('取消全选：清空所有选择')
      }

      this.$nextTick(() => {
        this._isTogglingSelectAll = false
      })
    },

    // 查看票据归并组详情
    async viewDocumentGroupDetail(group) {
      this.$Message.info(`查看归并组详情: ${group.groupName}`)
    },

    // 查看银证归并组详情
    async viewReceiptGroupDetail(group) {
      this.$Message.info(`查看归并组详情: ${group.groupName}`)
    },

    // 解散票据归并组
    async dissolveDocumentGroup(groupId) {
      this.$Confirm('确定要解散这个票据归并组吗？').then(async () => {
        try {
          const response = await fetch(`/api/merge-engine/documents/groups/${groupId}`, {
            method: 'DELETE',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json'
            }
          })
          const result = await response.json()
          if (result.success) {
            this.$Message.success('票据归并组解散成功')
            this.loadDocumentGroups()
          } else {
            this.$Message.error('解散失败: ' + result.msg)
          }
        } catch (error) {
          this.$Message.error('解散票据归并组失败: ' + error.message)
        }
      })
    },

    // 解散银证归并组
    async dissolveReceiptGroup(groupId) {
      this.$Confirm('确定要解散这个银证归并组吗？').then(async () => {
        try {
          const response = await fetch(`/api/merge-engine/receipts/groups/${groupId}`, {
            method: 'DELETE',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json'
            }
          })
          const result = await response.json()
          if (result.success) {
            this.$Message.success('银证归并组解散成功')
            this.loadReceiptGroups()
          } else {
            this.$Message.error('解散失败: ' + result.msg)
          }
        } catch (error) {
          this.$Message.error('解散银证归并组失败: ' + error.message)
        }
      })
    },

    // AI智能归并方法
    async loadUnmergedDocumentsForAi() {
      try {
        const response = await fetch('/api/ai-merge/documents/unmerged?limit=50', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.aiUnmergedDocuments = result.data || []
          this.$Message.success(`加载了${this.aiUnmergedDocuments.length}个未归并票据`)
        } else {
          this.$Message.error('加载失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('加载未归并票据失败: ' + error.message)
      }
    },

    async analyzeDocumentMergeWithAi() {
      if (this.aiAnalyzing) return

      try {
        this.aiAnalyzing = true
        this.$Message.info('🤖 AI正在分析票据数据，请稍候...')

        const billIds = this.aiUnmergedDocuments.map(doc => doc.id)
        const response = await fetch('/api/ai-merge/documents/analyze', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(billIds)
        })
        const result = await response.json()
        if (result.success) {
          this.aiDocumentSuggestions = result.data
          this.selectedAiDocumentGroups = [] // 重置选择
          this.$Message.success('✅ AI分析完成: ' + result.data.message)
        } else {
          this.$Message.error('❌ AI分析失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('❌ AI分析失败: ' + error.message)
      } finally {
        this.aiAnalyzing = false
      }
    },

    // AI建议选择相关方法
    selectAllAiDocumentGroups() {
      if (this.aiDocumentSuggestions && this.aiDocumentSuggestions.groups) {
        this.selectedAiDocumentGroups = this.aiDocumentSuggestions.groups.map((_, index) => index)
      }
    },

    clearAiDocumentGroupSelection() {
      this.selectedAiDocumentGroups = []
    },

    selectAllAiReceiptGroups() {
      if (this.aiReceiptSuggestions && this.aiReceiptSuggestions.groups) {
        this.selectedAiReceiptGroups = this.aiReceiptSuggestions.groups.map((_, index) => index)
      }
    },

    clearAiReceiptGroupSelection() {
      this.selectedAiReceiptGroups = []
    },

    // 执行选中的AI归并
    async executeSelectedAiDocumentMerge() {
      if (this.selectedAiDocumentGroups.length === 0) {
        this.$Message.error('请先选择要执行的归并组')
        return
      }

      try {
        const selectedGroups = this.selectedAiDocumentGroups.map(index => this.aiDocumentSuggestions.groups[index])
        const allBillIds = selectedGroups.reduce((acc, group) => acc.concat(group.billIds), [])

        this.$Message.info(`正在执行${selectedGroups.length}个归并组的归并操作...`)

        const response = await fetch('/api/ai-merge/documents/execute-selected', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            selectedGroups: selectedGroups,
            billIds: allBillIds
          })
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success('✅ 选中归并执行成功: ' + result.data.message)
          this.aiDocumentSuggestions = null
          this.selectedAiDocumentGroups = []
          this.loadUnmergedDocumentsForAi()
          this.loadDocumentGroups()
        } else {
          this.$Message.error('❌ 选中归并失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('❌ 选中归并失败: ' + error.message)
      }
    },

    async executeSelectedAiReceiptMerge() {
      if (this.selectedAiReceiptGroups.length === 0) {
        this.$Message.error('请先选择要执行的归并组')
        return
      }

      try {
        const selectedGroups = this.selectedAiReceiptGroups.map(index => this.aiReceiptSuggestions.groups[index])
        const allReceiptIds = selectedGroups.reduce((acc, group) => acc.concat(group.receiptIds), [])

        this.$Message.info(`正在执行${selectedGroups.length}个归并组的归并操作...`)

        const response = await fetch('/api/ai-merge/receipts/execute-selected', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            selectedGroups: selectedGroups,
            receiptIds: allReceiptIds
          })
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success('✅ 选中归并执行成功: ' + result.data.message)
          this.aiReceiptSuggestions = null
          this.selectedAiReceiptGroups = []
          this.loadUnmergedReceiptsForAi()
          this.loadReceiptGroups()
        } else {
          this.$Message.error('❌ 选中归并失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('❌ 选中归并失败: ' + error.message)
      }
    },

    async executeAiDocumentMerge() {
      try {
        const billIds = this.aiUnmergedDocuments.map(doc => doc.id)
        const response = await fetch('/api/ai-merge/documents/execute', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(billIds)
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success('AI归并完成: ' + result.data.message)
          this.aiDocumentSuggestions = null
          this.loadUnmergedDocumentsForAi()
          this.loadDocumentGroups()
        } else {
          this.$Message.error('AI归并失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('AI归并失败: ' + error.message)
      }
    },

    async autoMergeAllDocuments() {
      this.$Confirm('确定要执行一键智能归并吗？AI将自动分析并归并所有未归并的票据。').then(async () => {
        try {
          const response = await fetch('/api/ai-merge/auto-merge-all?limit=50', {
            method: 'POST',
            credentials: 'include'
          })
          const result = await response.json()
          if (result.success) {
            this.$Message.success('一键智能归并完成: ' + result.data.message)
            this.loadUnmergedDocumentsForAi()
            this.loadDocumentGroups()
          } else {
            this.$Message.error('一键归并失败: ' + result.msg)
          }
        } catch (error) {
          this.$Message.error('一键归并失败: ' + error.message)
        }
      })
    },

    async loadUnmergedReceiptsForAi() {
      try {
        const response = await fetch('/api/ai-merge/receipts/unmerged?limit=50', {
          credentials: 'include'
        })
        const result = await response.json()
        if (result.success) {
          this.aiUnmergedReceipts = result.data || []
          this.$Message.success(`加载了${this.aiUnmergedReceipts.length}个未归并银证`)
        } else {
          this.$Message.error('加载失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('加载未归并银证失败: ' + error.message)
      }
    },

    async analyzeReceiptMergeWithAi() {
      if (this.aiAnalyzingReceipts) return

      try {
        this.aiAnalyzingReceipts = true
        this.$Message.info('🤖 AI正在分析银证数据，请稍候...')

        const receiptIds = this.aiUnmergedReceipts.map(receipt => receipt.id)
        const response = await fetch('/api/ai-merge/receipts/analyze', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(receiptIds)
        })
        const result = await response.json()
        if (result.success) {
          this.aiReceiptSuggestions = result.data
          this.selectedAiReceiptGroups = [] // 重置选择
          this.$Message.success('✅ AI分析完成: ' + result.data.message)
        } else {
          this.$Message.error('❌ AI分析失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('❌ AI分析失败: ' + error.message)
      } finally {
        this.aiAnalyzingReceipts = false
      }
    },

    async executeAiReceiptMerge() {
      try {
        const receiptIds = this.aiUnmergedReceipts.map(receipt => receipt.id)
        const response = await fetch('/api/ai-merge/receipts/execute', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(receiptIds)
        })
        const result = await response.json()
        if (result.success) {
          this.$Message.success('AI归并完成: ' + result.data.message)
          this.aiReceiptSuggestions = null
          this.loadUnmergedReceiptsForAi()
          this.loadReceiptGroups()
        } else {
          this.$Message.error('AI归并失败: ' + result.msg)
        }
      } catch (error) {
        this.$Message.error('AI归并失败: ' + error.message)
      }
    },

    async autoMergeAllReceipts() {
      this.$Confirm('确定要执行一键智能归并吗？AI将自动分析并归并所有未归并的银证。').then(async () => {
        try {
          const response = await fetch('/api/ai-merge/auto-merge-all?limit=50', {
            method: 'POST',
            credentials: 'include'
          })
          const result = await response.json()
          if (result.success) {
            this.$Message.success('一键智能归并完成: ' + result.data.message)
            this.loadUnmergedReceiptsForAi()
            this.loadReceiptGroups()
          } else {
            this.$Message.error('一键归并失败: ' + result.msg)
          }
        } catch (error) {
          this.$Message.error('一键归并失败: ' + error.message)
        }
      })
    },


  }
}
</script>

<style lang="less" scoped>
.merge-panel, .group-panel {
  padding: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

.toolbar Button {
  margin-right: 10px;
}

.preview-section {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.preview-summary {
  margin-bottom: 15px;
}

.preview-summary .h-tag {
  margin-right: 10px;
}

.group-item {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.group-item h5 {
  margin: 0 0 10px 0;
  color: #333;
}

.h-btn-primary {
  background-color: #409eff !important;
  color: white !important;
}

/* AI智能归并样式 */
.ai-merge-section {
  padding: 20px;
}

.ai-info-panel {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.ai-info-panel h4 {
  margin: 0 0 10px 0;
  font-size: 18px;
}

.ai-info-panel p {
  margin: 0;
  opacity: 0.9;
}

.ai-suggestions {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.suggestion-summary {
  margin-bottom: 15px;
}

.suggestion-summary .h-tag {
  margin-right: 10px;
}

.suggestion-group {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.suggestion-group h5 {
  margin: 0 0 8px 0;
  color: #495057;
  font-weight: 600;
}

.suggestion-reason {
  color: #6c757d;
  font-style: italic;
  margin: 0 0 10px 0;
}

.suggested-items .h-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}

.unmerged-list {
  margin-top: 20px;
}

.unmerged-list h4 {
  margin-bottom: 15px;
  color: #495057;
}
</style>
