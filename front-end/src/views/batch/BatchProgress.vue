<template>
  <div class="batch-progress-container">
    <div class="page-header">
      <h2>批量处理进度</h2>
      <div class="header-actions">
        <Button @click="refreshProgress" :loading="refreshing">刷新</Button>
        <Button @click="goBack">返回</Button>
      </div>
    </div>

    <div class="progress-card" v-if="taskInfo">
      <div class="task-info">
        <h3>{{ taskInfo.taskName || '批量处理任务' }}</h3>
        <div class="task-meta">
          <span>任务ID: {{ taskInfo.taskId }}</span>
          <span>类型: {{ getImportTypeText(taskInfo.importType) }}</span>
          <span>创建时间: {{ formatTime(taskInfo.createdTime) }}</span>
        </div>
      </div>

      <div class="progress-section">
        <div class="progress-header">
          <span class="status-badge" :class="getStatusClass(taskInfo.status)">
            {{ getStatusText(taskInfo.status) }}
          </span>
          <span class="progress-text">{{ progressText }}</span>
        </div>
        
        <div class="progress-bar-container">
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: progressPercentage + '%' }"
              :class="getProgressClass(taskInfo.status)"
            ></div>
          </div>
          <span class="progress-percentage">{{ progressPercentage }}%</span>
        </div>
      </div>

      <div class="stats-section">
        <div class="stat-item">
          <div class="stat-value">{{ taskInfo.totalFiles || 0 }}</div>
          <div class="stat-label">总文件数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ taskInfo.totalImages || 0 }}</div>
          <div class="stat-label">总图片数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ taskInfo.successCount || 0 }}</div>
          <div class="stat-label">成功数量</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ taskInfo.failedCount || 0 }}</div>
          <div class="stat-label">失败数量</div>
        </div>
      </div>

      <div class="detail-stats" v-if="taskInfo.detailStats">
        <h4>处理详情</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <span class="detail-label">待处理:</span>
            <span class="detail-value">{{ taskInfo.detailStats.pending }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">处理中:</span>
            <span class="detail-value">{{ taskInfo.detailStats.processing }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">已完成:</span>
            <span class="detail-value">{{ taskInfo.detailStats.completed }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">失败:</span>
            <span class="detail-value">{{ taskInfo.detailStats.failed }}</span>
          </div>
        </div>
      </div>

      <div class="message-section" v-if="currentMessage">
        <div class="message-item" :class="messageClass">
          <Icon :type="messageIcon" />
          <span>{{ currentMessage }}</span>
          <span class="message-time">{{ formatTime(messageTime) }}</span>
        </div>
      </div>

      <div class="actions-section" v-if="showActions">
        <Button 
          type="primary" 
          @click="goToPreview"
          v-if="taskInfo.status === 'PREVIEWING'"
        >
          查看识别结果
        </Button>
        <Button 
          @click="retryTask"
          v-if="taskInfo.status === 'FAILED'"
        >
          重试任务
        </Button>
      </div>
    </div>

    <div class="loading-card" v-else-if="loading">
      <Spin size="large">
        <div>正在加载任务信息...</div>
      </Spin>
    </div>

    <div class="error-card" v-else-if="error">
      <Alert type="error" :desc="error" />
      <Button @click="refreshProgress" style="margin-top: 16px;">重新加载</Button>
    </div>
  </div>
</template>

<script>
import { subscribeBatchProgress, unsubscribeBatchProgress } from '@/utils/websocket'

export default {
  name: 'BatchProgress',
  data() {
    return {
      taskId: null,
      taskInfo: null,
      loading: true,
      refreshing: false,
      error: null,
      currentMessage: '',
      messageTime: null,
      messageClass: '',
      messageIcon: 'ios-information-circle',
      wsConnected: false
    }
  },
  computed: {
    progressPercentage() {
      if (!this.taskInfo) return 0
      return Math.round(this.taskInfo.progressPercentage || 0)
    },
    progressText() {
      if (!this.taskInfo) return ''
      
      const { status, processedImages, totalImages } = this.taskInfo
      
      if (status === 'UPLOADING') {
        return '正在上传文件...'
      } else if (status === 'PROCESSING') {
        return '正在处理文件...'
      } else if (status === 'RECOGNIZING') {
        return `正在识别... (${processedImages || 0}/${totalImages || 0})`
      } else if (status === 'PREVIEWING') {
        return '识别完成，可以预览结果'
      } else if (status === 'COMPLETED') {
        return '任务已完成'
      } else if (status === 'FAILED') {
        return '任务失败'
      }
      
      return ''
    },
    showActions() {
      return this.taskInfo && ['PREVIEWING', 'FAILED'].includes(this.taskInfo.status)
    }
  },
  created() {
    this.taskId = this.$route.params.taskId || this.$route.query.taskId
    if (!this.taskId) {
      this.$Message.error('缺少任务ID参数')
      this.goBack()
      return
    }
    
    this.loadTaskInfo()
    this.setupWebSocket()
  },
  beforeDestroy() {
    this.cleanupWebSocket()
  },
  methods: {
    async loadTaskInfo() {
      try {
        this.loading = true
        this.error = null
        
        const response = await this.$api.batch.getProgress(this.taskId)
        if (response.success) {
          this.taskInfo = response.data
        } else {
          throw new Error(response.message || '获取任务信息失败')
        }
      } catch (error) {
        this.error = error.message || '加载任务信息失败'
      } finally {
        this.loading = false
      }
    },
    
    async refreshProgress() {
      this.refreshing = true
      await this.loadTaskInfo()
      this.refreshing = false
    },
    
    setupWebSocket() {
      subscribeBatchProgress(this.taskId, {
        onProgress: (data) => {
          this.handleProgressUpdate(data)
        },
        onComplete: (data) => {
          this.handleCompletion(data)
        },
        onError: (data) => {
          this.handleError(data)
        }
      })
      this.wsConnected = true
    },
    
    cleanupWebSocket() {
      if (this.wsConnected) {
        unsubscribeBatchProgress(this.taskId)
        this.wsConnected = false
      }
    },
    
    handleProgressUpdate(data) {
      this.currentMessage = data.message
      this.messageTime = new Date()
      this.messageClass = 'info'
      this.messageIcon = 'ios-information-circle'
      
      // 刷新任务信息
      this.refreshProgress()
    },
    
    handleCompletion(data) {
      this.currentMessage = data.message
      this.messageTime = new Date()
      this.messageClass = 'success'
      this.messageIcon = 'ios-checkmark-circle'
      
      this.$Message.success('任务处理完成！')
      this.refreshProgress()
    },
    
    handleError(data) {
      this.currentMessage = data.message
      this.messageTime = new Date()
      this.messageClass = 'error'
      this.messageIcon = 'ios-close-circle'
      
      this.$Message.error('任务处理失败')
      this.refreshProgress()
    },
    
    getImportTypeText(type) {
      const typeMap = {
        'BANK_RECEIPT': '银行回单',
        'INVOICE': '发票'
      }
      return typeMap[type] || type
    },
    
    getStatusText(status) {
      const statusMap = {
        'UPLOADING': '上传中',
        'PROCESSING': '处理中',
        'RECOGNIZING': '识别中',
        'PREVIEWING': '待预览',
        'SAVING': '保存中',
        'COMPLETED': '已完成',
        'PARTIAL_SUCCESS': '部分成功',
        'FAILED': '失败',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || status
    },

    getStatusClass(status) {
      const classMap = {
        'UPLOADING': 'status-uploading',
        'PROCESSING': 'status-processing',
        'RECOGNIZING': 'status-recognizing',
        'PREVIEWING': 'status-previewing',
        'SAVING': 'status-saving',
        'COMPLETED': 'status-completed',
        'FAILED': 'status-failed',
        'CANCELLED': 'status-cancelled'
      }
      return classMap[status] || ''
    },

    getProgressClass(status) {
      if (status === 'FAILED') return 'progress-error'
      if (status === 'COMPLETED') return 'progress-success'
      if (status === 'SAVING') return 'progress-saving'
      return 'progress-active'
    },
    
    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleString()
    },
    
    goToPreview() {
      this.$router.push(`/batch/preview/${this.taskId}`)
    },
    
    retryTask() {
      // 彻底简化版本：立即跳转
      console.log('🔄 重试任务被点击，taskId:', this.taskId)

      // 立即跳转到预览页面
      this.$router.push({
        path: `/batch/preview/${this.taskId}`,
        query: {
          highlightFailed: 'true' // 标记需要高亮显示失败记录
        }
      })
    },
    
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.batch-progress-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.progress-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.task-info h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.task-meta {
  display: flex;
  gap: 20px;
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.progress-section {
  margin-bottom: 24px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-uploading { background: #e6f7ff; color: #1890ff; }
.status-processing { background: #fff7e6; color: #fa8c16; }
.status-recognizing { background: #f6ffed; color: #52c41a; }
.status-previewing { background: #f9f0ff; color: #722ed1; }
.status-saving { background: #fff1f0; color: #fa541c; }
.status-completed { background: #f6ffed; color: #52c41a; }
.status-failed { background: #fff2f0; color: #ff4d4f; }
.status-cancelled { background: #f5f5f5; color: #8c8c8c; }

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.progress-active { background: #1890ff; }
.progress-saving { background: #fa541c; }
.progress-success { background: #52c41a; }
.progress-error { background: #ff4d4f; }

.progress-percentage {
  font-weight: 500;
  color: #333;
  min-width: 40px;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.detail-stats h4 {
  margin: 0 0 12px 0;
  color: #333;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.detail-label {
  color: #666;
}

.detail-value {
  font-weight: 500;
  color: #333;
}

.message-section {
  margin: 20px 0;
}

.message-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
}

.message-item.info {
  background: #e6f7ff;
  color: #1890ff;
}

.message-item.success {
  background: #f6ffed;
  color: #52c41a;
}

.message-item.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.message-time {
  margin-left: auto;
  font-size: 12px;
  opacity: 0.7;
}

.actions-section {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.loading-card, .error-card {
  background: white;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
</style>
