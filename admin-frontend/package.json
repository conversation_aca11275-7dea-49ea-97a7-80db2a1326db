{"name": "financial-admin-frontend", "version": "1.0.0", "description": "财务系统管理后台", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.5", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "^2.4.4", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.15", "vue-echarts": "^6.6.1", "vue-router": "^4.2.5"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20.11.5", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "postcss": "^8.4.33", "prettier": "^3.2.4", "sass-embedded": "^1.89.2", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "~5.3.3", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.11", "vue-tsc": "^1.8.27"}, "engines": {"node": ">=18.0.0"}}