<template>
  <div class="account-sets-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">账套管理</h1>
      <p class="page-description">管理系统中的所有账套信息</p>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="search-bar">
      <div class="search-left">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索账套（公司名称、信用代码）"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-button type="primary" @click="handleSearch" style="margin-left: 10px">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button @click="handleReset" style="margin-left: 10px">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
      
      <div class="search-right">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新建账套
        </el-button>
      </div>
    </div>

    <!-- 账套列表 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="accountSetsList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="companyName" label="公司名称" width="200" />
        
        <el-table-column prop="creditCode" label="信用代码" width="180" />
        
        <el-table-column label="会计准则" width="120">
          <template #default="scope">
            {{ getAccountingStandardsName(scope.row.accountingStandards) }}
          </template>
        </el-table-column>
        
        <el-table-column label="增值税类型" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.vatType === 0 ? 'warning' : 'success'">
              {{ scope.row.vatType === 0 ? '小规模' : '一般纳税人' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="address" label="地址" width="150" show-overflow-tooltip />
        
        <el-table-column label="创建者" width="120">
          <template #default="scope">
            <div v-if="scope.row.creatorName">
              <div>{{ scope.row.creatorName }}</div>
              <div class="text-gray-400 text-sm">{{ scope.row.creatorMobile }}</div>
            </div>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="用户数量" width="100">
          <template #default="scope">
            <el-badge :value="scope.row.userCount" class="item">
              <el-icon><User /></el-icon>
            </el-badge>
          </template>
        </el-table-column>
        
        <el-table-column prop="enableDate" label="启用日期" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.enableDate, 'YYYY-MM-DD') }}
          </template>
        </el-table-column>
        
        <el-table-column prop="createDate" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.createDate) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button link size="small" @click="handleView(scope.row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button link size="small" @click="handleEdit(scope.row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button link size="small" type="danger" @click="handleDelete(scope.row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 账套详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="账套详情"
      width="900px"
      :before-close="handleDetailDialogClose"
    >
      <div v-if="currentAccountSets" class="account-sets-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="账套ID">{{ currentAccountSets.id }}</el-descriptions-item>
          <el-descriptions-item label="公司名称">{{ currentAccountSets.companyName }}</el-descriptions-item>
          <el-descriptions-item label="信用代码">{{ currentAccountSets.creditCode || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="会计准则">{{ getAccountingStandardsName(currentAccountSets.accountingStandards) }}</el-descriptions-item>
          <el-descriptions-item label="增值税类型">
            <el-tag :type="currentAccountSets.vatType === 0 ? 'warning' : 'success'">
              {{ currentAccountSets.vatType === 0 ? '小规模纳税人' : '一般纳税人' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="地址">{{ currentAccountSets.address || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="启用日期">{{ formatDate(currentAccountSets.enableDate, 'YYYY-MM-DD') }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(currentAccountSets.createDate) }}</el-descriptions-item>
          <el-descriptions-item label="创建者">
            {{ currentAccountSets.creatorName }}（{{ currentAccountSets.creatorMobile }}）
          </el-descriptions-item>
          <el-descriptions-item label="科目编码">{{ currentAccountSets.encoding || '4-2-2-2' }}</el-descriptions-item>
        </el-descriptions>
        
        <h3 style="margin-top: 20px; margin-bottom: 10px">账套用户</h3>
        <el-table :data="currentAccountSets.userList" style="width: 100%">
          <el-table-column prop="mobile" label="手机号" />
          <el-table-column prop="realName" label="真实姓名" />
          <el-table-column prop="nickname" label="昵称" />
          <el-table-column label="角色">
            <template #default="scope">
              <el-tag :type="getRoleTagType(scope.row.role)">
                {{ getRoleDisplayName(scope.row.role) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createDate" label="加入时间">
            <template #default="scope">
              {{ formatDate(scope.row.createDate) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button link size="small" type="danger" @click="handleRemoveUser(scope.row)">
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 创建/编辑账套对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEdit ? '编辑账套' : '新建账套'"
      width="600px"
      :before-close="handleFormDialogClose"
    >
      <el-form
        ref="accountSetsFormRef"
        :model="accountSetsForm"
        :rules="accountSetsFormRules"
        label-width="120px"
      >
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="accountSetsForm.companyName" placeholder="请输入公司名称" />
        </el-form-item>
        
        <el-form-item label="启用日期" prop="enableDate">
          <el-date-picker
            v-model="accountSetsForm.enableDate"
            type="date"
            placeholder="选择启用日期"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="信用代码" prop="creditCode">
          <el-input v-model="accountSetsForm.creditCode" placeholder="请输入统一社会信用代码" />
        </el-form-item>
        
        <el-form-item label="会计准则" prop="accountingStandards">
          <el-select v-model="accountSetsForm.accountingStandards" placeholder="选择会计准则" style="width: 100%">
            <el-option label="企业会计准则" :value="0" />
            <el-option label="小企业会计准则" :value="1" />
            <el-option label="民间非营利组织会计制度" :value="2" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="增值税类型" prop="vatType">
          <el-select v-model="accountSetsForm.vatType" placeholder="选择增值税类型" style="width: 100%">
            <el-option label="小规模纳税人" :value="0" />
            <el-option label="一般纳税人" :value="1" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="地址" prop="address">
          <el-input v-model="accountSetsForm.address" placeholder="请输入公司地址" />
        </el-form-item>
        
        <el-form-item v-if="!isEdit" label="创建者" prop="creatorId">
          <el-select
            v-model="accountSetsForm.creatorId"
            placeholder="选择创建者"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="`${user.realName}（${user.mobile}）`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  View,
  Edit,
  Delete,
  User
} from '@element-plus/icons-vue'
import { accountSetsApi, userApi } from '@/api'
import type { AccountSet, User as UserType, AccountSetForm, CreateAccountSetsData } from '@/types'
import { formatDate as utilFormatDate } from '@/utils'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const accountSetsList = ref([])
const userList = ref([])
const selectedAccountSets = ref([])
const currentAccountSets = ref(null)
const currentEditAccountSetsId = ref(null)

// 对话框状态
const detailDialogVisible = ref(false)
const formDialogVisible = ref(false)
const isEdit = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 账套表单
const accountSetsForm = reactive({
  companyName: '',
  enableDate: '',
  creditCode: '',
  accountingStandards: 0,
  vatType: 0,
  address: '',
  creatorId: null
})

const accountSetsFormRef = ref<FormInstance>()

// 表单验证规则
const accountSetsFormRules: FormRules = {
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  enableDate: [
    { required: true, message: '请选择启用日期', trigger: 'change' }
  ],
  creatorId: [
    { required: true, message: '请选择创建者', trigger: 'change' }
  ]
}

// 方法
const loadAccountSetsList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword || undefined
    }
    
    const response = await accountSetsApi.getAccountSetsList(params)
    if (response.success) {
      accountSetsList.value = response.data.list
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '获取账套列表失败')
    }
  } catch (error) {
    ElMessage.error('获取账套列表失败')
  } finally {
    loading.value = false
  }
}

const loadUserList = async () => {
  try {
    const response = await userApi.getUserList({ page: 1, size: 1000 })
    if (response.success) {
      userList.value = response.data.list
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadAccountSetsList()
}

const handleReset = () => {
  searchForm.keyword = ''
  pagination.current = 1
  loadAccountSetsList()
}

const handleCreate = () => {
  isEdit.value = false
  resetAccountSetsForm()
  formDialogVisible.value = true
}

const handleView = async (accountSets: any) => {
  try {
    const response = await accountSetsApi.getAccountSetsDetail(accountSets.id)
    if (response.success) {
      currentAccountSets.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取账套详情失败')
    }
  } catch (error) {
    ElMessage.error('获取账套详情失败')
  }
}

const handleEdit = (accountSets: any) => {
  isEdit.value = true
  accountSetsForm.companyName = accountSets.companyName
  accountSetsForm.enableDate = accountSets.enableDate
  accountSetsForm.creditCode = accountSets.creditCode
  accountSetsForm.accountingStandards = accountSets.accountingStandards
  accountSetsForm.vatType = accountSets.vatType
  accountSetsForm.address = accountSets.address
  currentEditAccountSetsId.value = accountSets.id
  formDialogVisible.value = true
}

const handleDelete = async (accountSets: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除账套 ${accountSets.companyName} 吗？此操作不可恢复！`,
      '删除账套',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await accountSetsApi.deleteAccountSets(accountSets.id)
    if (response.success) {
      ElMessage.success('账套删除成功')
      loadAccountSetsList()
    } else {
      ElMessage.error(response.message || '删除账套失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除账套失败')
    }
  }
}

const handleRemoveUser = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要从账套中移除用户 ${user.realName}(${user.mobile}) 吗？`,
      '移除用户',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await accountSetsApi.removeUserFromAccountSets(currentAccountSets.value.id, user.userId)
    if (response.success) {
      ElMessage.success('用户移除成功')
      // 重新加载账套详情
      handleView(currentAccountSets.value)
    } else {
      ElMessage.error(response.message || '移除用户失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除用户失败')
    }
  }
}

const handleSubmit = async () => {
  if (!accountSetsFormRef.value) return
  
  try {
    await accountSetsFormRef.value.validate()
    submitting.value = true
    
    let response
    if (isEdit.value) {
      response = await accountSetsApi.updateAccountSets(currentEditAccountSetsId.value, accountSetsForm)
    } else {
      response = await accountSetsApi.createAccountSets(accountSetsForm)
    }
    
    if (response.success) {
      ElMessage.success(isEdit.value ? '账套更新成功' : '账套创建成功')
      formDialogVisible.value = false
      loadAccountSetsList()
    } else {
      ElMessage.error(response.message || (isEdit.value ? '更新账套失败' : '创建账套失败'))
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

const resetAccountSetsForm = () => {
  accountSetsForm.companyName = ''
  accountSetsForm.enableDate = ''
  accountSetsForm.creditCode = ''
  accountSetsForm.accountingStandards = 0
  accountSetsForm.vatType = 0
  accountSetsForm.address = ''
  accountSetsForm.creatorId = null
  currentEditAccountSetsId.value = null
}

const handleSelectionChange = (selection: any[]) => {
  selectedAccountSets.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  loadAccountSetsList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadAccountSetsList()
}

const handleDetailDialogClose = () => {
  detailDialogVisible.value = false
  currentAccountSets.value = null
}

const handleFormDialogClose = () => {
  formDialogVisible.value = false
  resetAccountSetsForm()
}

const getAccountingStandardsName = (standards: number) => {
  const standardsNames = {
    0: '企业会计准则',
    1: '小企业会计准则',
    2: '民间非营利组织会计制度'
  }
  return standardsNames[standards] || '未知'
}

const getRoleTagType = (role: string) => {
  const roleTypes = {
    Manager: 'danger',
    Accountant: 'primary',
    Cashier: 'warning',
    View: 'info'
  }
  return roleTypes[role] || 'info'
}

const getRoleDisplayName = (role: string) => {
  const roleNames = {
    Manager: '管理员',
    Accountant: '会计',
    Cashier: '出纳',
    View: '查看者'
  }
  return roleNames[role] || role
}

const formatDate = (date: string, format: string = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return '-'
  return utilFormatDate(date, format)
}

// 生命周期
onMounted(() => {
  loadAccountSetsList()
  loadUserList()
})
</script>

<style scoped>
.account-sets-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #303133;
}

.page-description {
  color: #606266;
  margin: 0;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-left {
  display: flex;
  align-items: center;
}

.table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-container {
  padding: 16px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.account-sets-detail {
  padding: 10px 0;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-sm {
  font-size: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
