<template>
  <div class="admin-layout">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <el-icon class="logo-img"><Management /></el-icon>
          <span v-show="!sidebarCollapsed" class="logo-text">财务管理后台</span>
        </div>
      </div>
      
      <nav class="sidebar-nav">
        <el-menu
          :default-active="activeMenu"
          :collapse="sidebarCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <template v-for="item in menuItems" :key="item.path">
            <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.path">
              <template #title>
                <el-icon v-if="item.icon">
                  <component :is="item.icon" />
                </el-icon>
                <span>{{ item.title }}</span>
              </template>
              <el-menu-item
                v-for="child in item.children"
                :key="child.path"
                :index="child.path"
              >
                <el-icon v-if="child.icon">
                  <component :is="child.icon" />
                </el-icon>
                <span>{{ child.title }}</span>
              </el-menu-item>
            </el-sub-menu>
            
            <el-menu-item v-else :index="item.path">
              <el-icon v-if="item.icon">
                <component :is="item.icon" />
              </el-icon>
              <span>{{ item.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </nav>
    </aside>
    
    <!-- 主内容区 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <header class="header">
        <div class="header-left">
          <el-button
            link
            @click="toggleSidebar"
            class="sidebar-toggle"
          >
            <el-icon>
              <Fold v-if="!sidebarCollapsed" />
              <Expand v-else />
            </el-icon>
          </el-button>
          
          <!-- 面包屑导航 -->
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item
              v-for="item in breadcrumbItems"
              :key="item.path"
              :to="item.path === $route.path ? undefined : item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <!-- 全屏切换 -->
          <el-tooltip content="全屏" placement="bottom">
            <el-button link @click="toggleFullscreen" class="header-action">
              <el-icon>
                <FullScreen />
              </el-icon>
            </el-button>
          </el-tooltip>
          
          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand" class="user-dropdown">
            <div class="user-info">
              <el-avatar :size="32" :src="userInfo?.avatarUrl">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ userInfo?.realName || userInfo?.username }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人信息
                </el-dropdown-item>
                <el-dropdown-item command="changePassword">
                  <el-icon><Lock /></el-icon>
                  修改密码
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>
      
      <!-- 页面内容 -->
      <main class="content">
        <router-view v-slot="{ Component, route }">
          <transition name="fade" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </main>
    </div>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="changePasswordVisible"
      title="修改密码"
      width="400px"
      :close-on-click-modal="false"
      @close="handlePasswordDialogClose"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入原密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="changePasswordVisible = false">取消</el-button>
        <el-button type="primary" @click="handleChangePassword" :loading="passwordLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import type { MenuItem } from '@/types'
import { Management } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const sidebarCollapsed = ref(false)
const changePasswordVisible = ref(false)
const passwordLoading = ref(false)
const cachedViews = ref<string[]>([])

// 修改密码表单
const passwordFormRef = ref<FormInstance>()
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const passwordRules: FormRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const userInfo = computed(() => authStore.userInfo)
const activeMenu = computed(() => route.path)

// 菜单项
const menuItems = computed<MenuItem[]>(() => {
  const items: MenuItem[] = [
    {
      path: '/dashboard',
      name: 'Dashboard',
      title: '仪表盘',
      icon: 'DataBoard'
    },
    {
      path: '/users',
      name: 'UserManagement',
      title: '用户管理',
      icon: 'User',
      permission: 'user:view'
    },
    {
      path: '/accounts',
      name: 'AccountManagement',
      title: '账套管理',
      icon: 'Files',
      permission: 'account:view'
    },
    {
      path: '/ai-configs',
      name: 'AiConfigManagement',
      title: 'AI配置管理',
      icon: 'MagicStick',
      permission: 'ai:view'
    },
    {
      path: '/templates',
      name: 'TemplateManagement',
      title: '字段映射模板',
      icon: 'Document',
      permission: 'template:view'
    },
    {
      path: '/system',
      name: 'SystemManagement',
      title: '系统管理',
      icon: 'Setting',
      permission: 'system:view',
      children: [
        {
          path: '/system/settings',
          name: 'SystemSettings',
          title: '系统设置',
          permission: 'system:view'
        },
        {
          path: '/system/logs',
          name: 'OperationLogs',
          title: '操作日志',
          permission: 'log:view'
        },
        {
          path: '/system/admins',
          name: 'AdminUsers',
          title: '管理员账号',
          permission: 'system:admin'
        }
      ]
    }
  ]
  
  // 过滤没有权限的菜单项
  return items.filter(item => {
    if (item.permission && !authStore.hasPermission(item.permission)) {
      return false
    }
    if (item.children) {
      item.children = item.children.filter(child => 
        !child.permission || authStore.hasPermission(child.permission)
      )
    }
    return true
  })
})

// 面包屑导航
const breadcrumbItems = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta.title as string
  }))
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'changePassword':
      changePasswordVisible.value = true
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleChangePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true

    await authStore.changePassword(
      passwordForm.value.oldPassword,
      passwordForm.value.newPassword
    )

    ElMessage.success('密码修改成功')
    changePasswordVisible.value = false

    // 重置表单
    passwordForm.value = {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    }

    // 清除表单验证状态
    if (passwordFormRef.value) {
      passwordFormRef.value.resetFields()
    }
  } catch (error: any) {
    console.error('Change password error:', error)
    const errorMessage = error?.response?.data?.message || error?.message || '密码修改失败'
    ElMessage.error(errorMessage)
  } finally {
    passwordLoading.value = false
  }
}

const handlePasswordDialogClose = () => {
  // 重置表单
  passwordForm.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  }

  // 清除表单验证状态
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
}

const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await authStore.logout()
      router.push('/login')
      ElMessage.success('退出登录成功')
    } catch (error) {
      console.error('Logout error:', error)
    }
  })
}

// 监听路由变化，管理缓存视图
watch(
  () => route.name,
  (newName) => {
    if (newName && route.meta.keepAlive) {
      if (!cachedViews.value.includes(newName as string)) {
        cachedViews.value.push(newName as string)
      }
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.admin-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  width: 240px;
  background: #001529;
  transition: width 0.3s ease;
  
  &.collapsed {
    width: 64px;
  }
  
  .sidebar-header {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid #1f2937;
    
    .logo {
      display: flex;
      align-items: center;
      color: white;
      
      .logo-img {
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }
      
      .logo-text {
        font-size: 18px;
        font-weight: 600;
        white-space: nowrap;
      }
    }
  }
  
  .sidebar-nav {
    height: calc(100vh - 60px);
    overflow-y: auto;
    
    .sidebar-menu {
      border: none;
      background: transparent;
      
      :deep(.el-menu-item),
      :deep(.el-sub-menu__title) {
        color: #d1d5db;
        
        &:hover {
          background-color: #1f2937;
          color: #60a5fa;
        }
        
        &.is-active {
          background-color: #3b82f6;
          color: white;
        }
      }
      
      :deep(.el-sub-menu .el-menu-item) {
        background-color: #111827;
        
        &:hover {
          background-color: #1f2937;
        }
        
        &.is-active {
          background-color: #3b82f6;
        }
      }
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .header-left {
    display: flex;
    align-items: center;
    
    .sidebar-toggle {
      margin-right: 20px;
      font-size: 18px;
    }
    
    .breadcrumb {
      font-size: 14px;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .header-action {
      font-size: 18px;
      color: #6b7280;
      
      &:hover {
        color: #3b82f6;
      }
    }
    
    .user-dropdown {
      .user-info {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 6px;
        transition: background-color 0.2s;
        
        &:hover {
          background-color: #f3f4f6;
        }
        
        .username {
          font-size: 14px;
          color: #374151;
        }
        
        .dropdown-icon {
          font-size: 12px;
          color: #9ca3af;
        }
      }
    }
  }
}

.content {
  flex: 1;
  overflow: auto;
  background: #f5f7fa;
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
