import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置 NProgress
NProgress.configure({ showSpinner: false })

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/LoginView.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/AdminLayout.vue'),
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/DashboardView.vue'),
        meta: {
          title: '仪表盘',
          icon: 'DataBoard',
          affix: true
        }
      },
      {
        path: 'users',
        name: 'UserManagement',
        component: () => import('@/views/users/UserManagementView.vue'),
        meta: {
          title: '用户管理',
          icon: 'User',
          permission: 'user:view'
        }
      },
      {
        path: 'accounts',
        name: 'AccountManagement',
        component: () => import('@/views/account-sets/AccountSetsManagementView.vue'),
        meta: {
          title: '账套管理',
          icon: 'Files',
          permission: 'account:view'
        }
      },
      {
        path: 'ai-configs',
        name: 'AiConfigManagement',
        component: () => import('@/views/ai-config/AiConfigListView.vue'),
        meta: {
          title: 'AI配置管理',
          icon: 'MagicStick',
          permission: 'ai:view'
        }
      },
      {
        path: 'templates',
        name: 'TemplateManagement',
        component: () => import('@/views/template/TemplateListView.vue'),
        meta: {
          title: '字段映射模板',
          icon: 'Document',
          permission: 'template:view'
        }
      },
      {
        path: 'templates/:id',
        name: 'TemplateDetail',
        component: () => import('@/views/template/TemplateDetailView.vue'),
        meta: {
          title: '模板详情',
          hideInMenu: true,
          permission: 'template:view'
        }
      },
      {
        path: 'system',
        name: 'SystemManagement',
        meta: {
          title: '系统管理',
          icon: 'Setting',
          permission: 'system:view'
        },
        children: [
          {
            path: 'settings',
            name: 'SystemSettings',
            component: () => import('@/views/system/SystemSettingsView.vue'),
            meta: {
              title: '系统设置',
              permission: 'system:view'
            }
          },
          {
            path: 'logs',
            name: 'OperationLogs',
            component: () => import('@/views/system/OperationLogsView.vue'),
            meta: {
              title: '操作日志',
              permission: 'log:view'
            }
          },
          {
            path: 'admins',
            name: 'AdminUsers',
            component: () => import('@/views/system/AdminUsersView.vue'),
            meta: {
              title: '管理员账号',
              permission: 'system:admin'
            }
          }
        ]
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/profile/ProfileView.vue'),
        meta: {
          title: '个人资料',
          hideInMenu: true
        }
      }
    ]
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/ForbiddenView.vue'),
    meta: {
      title: '权限不足',
      hideInMenu: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/NotFoundView.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory('/'),
  routes,
  scrollBehavior: () => ({ top: 0 })
})

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  NProgress.start()

  const authStore = useAuthStore()
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 财务系统管理后台`
  }

  // 检查登录状态
  if (requiresAuth) {
    if (!authStore.isLoggedIn) {
      // 尝试从本地存储恢复登录状态
      const token = localStorage.getItem('admin_token')
      if (token) {
        try {
          await authStore.getCurrentUser()
        } catch (error) {
          // Token 无效，清除并跳转到登录页
          authStore.logout()
          next('/login')
          return
        }
      } else {
        next('/login')
        return
      }
    }

    // 检查权限
    if (to.meta.permission && !authStore.hasPermission(to.meta.permission as string)) {
      next('/403')
      return
    }
  } else if (to.path === '/login' && authStore.isLoggedIn) {
    // 已登录用户访问登录页，重定向到首页
    next('/')
    return
  }

  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
